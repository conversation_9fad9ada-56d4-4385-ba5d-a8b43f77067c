Strategy Name: Statistical Arbitrage Carry Trade Strategy
Strategy Type: Funding Arbitrage

# 1. STRATEGY OVERVIEW
Core Principle: Exploit funding rate differentials in cryptocurrency perpetual futures markets by taking long positions in assets with negative funding rates and short positions in assets with positive funding rates.

Expected Edge: Funding rates in perpetual futures markets fluctuate around zero. When funding rates deviate significantly from zero, they tend to revert, providing profit opportunities for carry trades.

Time Horizon: Medium-term

Market Conditions: Performs best in volatile markets with high funding rate dispersion; effective in both bull and bear markets as it's market-neutral.

# 2. UNIVERSE SELECTION
Selection Criteria:
- Contracts: Perpetual futures markets only
- Volume Filter: Minimum 5-day average volume of $1,000,000 USD
- Volatility Filter: Minimum volatility of 5% to exclude stablecoins
- Listing Age: Exclude coins listed within the last 60 days
- Data Availability: Minimum 60 days of historical data required
- Funding Rate Bounds: No funding bounds

Implementation Details:
- Data Source: Exchange API for market data and funding rates
- Refresh Frequency: Daily universe refresh
- Fallback Mechanism: If primary data unavailable, use previous day's universe

# 3. FEATURE ENGINEERING

Primary Features:
- Annualized Funding Rate: Annualized funding rate. Bybit, Binance, OKX uses 8-hour funding rates. Hyperliquid uses 1-hour funding rates. Make sure the annualization factor is correct for 365-day markets in crypto, and for each exchange with different intervals.
- Adjusted annualized funding rate: Annualized funding rate minus trading cost adjustment (10.95% annualized). For new positions, apply trading cost adjustment with floor protection to prevent sign flips. For held positions, use raw annualized funding rates (no cost penalty).
- Weighted Volatility: Weighted combination of 10-day, 30-day, and 60-day annualized volatility
- Beta: 60-day rolling beta against BTC for market neutrality

Feature Validation:
- Data Quality Checks: Validate completeness and consistency
- Normalization: Ensure features are properly scaled

Feature Combination:
- Weighting Scheme: Sigmoid function y = tanh(2x), where x = adjusted annualized funding rate (in decimal form), y = EV weight
- Interaction Terms: None
- Feature Decay: No decay

# 4. POSITION SELECTION

Selection Process:
- Signal Threshold: Minimum expected value threshold after costs
- Ranking System: Three-tier ranking by:
- Primary: EV weight (sigmoid-weighted adjusted funding)
- Secondary: Raw funding rate magnitude
- Tertiary: 5-day average volume (tie-breaker)
- Long/Short Classification:
- Long Positions: Top 5 most negative adjusted funding rates
- Short Positions: Top 5 most positive adjusted funding rates

Selection Constraints:
- Minimum Positions: At least 5 positions per leg for diversification
- EV Threshold: Only select positions with positive expected value
- Balance Requirement: Maintain roughly equal long/short exposure

# 5. POSITION SIZING
Sizing Methodology:
- Base Approach: Volatility targeting with sigmoid weighting
- Target Volatility: 0.30 (30% annualized)
- Position Limits: Maximum 25% of capital per position
- Risk Controls:

- Beta Neutrality: Yes, with ±0.05 tolerance
- Leverage Limits: 0.05x to 5.0x per position

# 6. EXIT RULES
Profit Taking:
- Target Levels: No fixed profit targets
- Scaling Out: No partial exits
- Time-Based Exits: Reassess at each rebalancing period

Stop Losses:
- Hard stop loss if entire portfolio drawdown equal or more than 30%

Rebalancing:
- Schedule: Daily rebalancing at 23:00 UTC
- Threshold-Based: Rebalance when position weight deviates by >5% from target
- Buffer Zones: 5% tolerance before adjusting positions

# 7. CONFIGURATION PARAMETERS
