Strategy Name: Statistical Arbitrage Carry Trade Strategy
Strategy Type: Funding Arbitrage

# 1. STRATEGY OVERVIEW
Core Principle: Exploit funding rate differentials in cryptocurrency perpetual futures markets by taking long positions in assets with negative funding rates and short positions in assets with positive funding rates.

Expected Edge: Funding rates in perpetual futures markets fluctuate around zero. When funding rates deviate significantly from zero, they tend to revert, providing profit opportunities for carry trades.

Time Horizon: Medium-term

Market Conditions: Performs best in volatile markets with high funding rate dispersion; effective in both bull and bear markets as it's market-neutral.

# 2. UNIVERSE SELECTION

## 2.1 Data Fetching
- **Data Source**: Exchange API (Bybit, Binance, OKX, Hyperliquid)
- **Markets**: Fetch all available perpetual futures markets
- **Tickers**: Fetch current price and volume data
- **Funding History**: Fetch 3-day funding rate history for each symbol

## 2.2 Selection Criteria
- **Contracts**: Perpetual futures markets only
- **Volume Filter**: Minimum 5-day average volume of $1,000,000 USD
- **Volatility Filter**: Minimum volatility of 5% to exclude stablecoins
- **Listing Age**: Exclude coins listed within the last 60 days
- **Data Availability**: Minimum 60 days of historical data required
- **Funding Rate Bounds**: No funding rate bounds filter

## 2.3 Implementation Details
- **Refresh Frequency**: Daily universe refresh at rebalancing time
- **Concurrent Processing**: Batch processing of symbols for efficiency
- **Error Handling**: Skip symbols with invalid data, continue with valid ones
- **Caching**: Cache OHLCV and funding data to reduce API calls

# 3. FEATURE ENGINEERING

## 3.1 Funding Rate Processing
- **3-Day Mean Calculation**: Calculate mean of last 3 funding rate entries
- **Data Validation**: Validate each funding rate entry, skip invalid ones
- **Minimum History**: Require at least 3 valid funding rate entries

## 3.2 Annualized Funding Rate Calculation
**Exchange-Specific Annualization**:
- **Bybit, Binance, OKX**: 8-hour funding intervals → multiply by 1095 (365×24/8)
- **Hyperliquid**: 1-hour funding intervals → multiply by 8760 (365×24/1)

**Formula**: `annualized_funding_3d = mean_funding_3d × annualization_factor`

## 3.3 Adjusted Annualized Funding Rate
**Trading Cost Adjustment**:
- **Fixed Cost**: 10.95% annualized (0.1095) for all exchanges
- **Floor Protection**: Prevents sign flips during cost adjustment
  - Positive rates: `max(0, annualized_funding - 0.1095)`
  - Negative rates: `min(0, annualized_funding + 0.1095)`

**Position-Specific Treatment**:
- **New Positions**: Apply trading cost adjustment with floor protection
- **Held Positions**: Use raw annualized funding rates (no cost penalty)

## 3.4 Volatility Features
- **Weighted Volatility**: Weighted combination of 10-day, 30-day, and 60-day annualized volatility
  - Weights: 0.2 × 10d + 0.5 × 30d + 0.3 × 60d
- **Beta Calculation**: DISABLED - No beta calculations performed for this strategy
- **Fallback Values**: Default volatility 20%

## 3.5 Feature Validation
- **Data Quality Checks**: Validate completeness and consistency of all features
- **Volume Validation**: Ensure volume data is in correct units (USDT)
- **Price Validation**: Validate price data for reasonableness

# 4. POSITION SELECTION

## 4.1 Cost Adjustment for Current Positions
- **Current Positions**: Use raw annualized funding rates (no cost penalty)
- **New Positions**: Use cost-adjusted annualized funding rates

## 4.2 Long/Short Classification
- **Long Candidates**: Coins with negative adjusted funding rates (we get paid to long)
- **Short Candidates**: Coins with positive adjusted funding rates (we get paid to short)
- **Excluded**: Coins with zero adjusted funding rates

## 4.3 Triple-Tier Ranking System
**Ranking Method**: Three-tier ranking system for optimal position selection
1. **Primary Ranking**: Adjusted annualized funding rate
   - **Long Positions**: Most negative adjusted funding rate first
   - **Short Positions**: Most positive adjusted funding rate first
2. **Secondary Ranking**: Raw annualized funding rate (tie-breaker for equal adjusted rates)
   - **Long Positions**: Most negative raw funding rate first
   - **Short Positions**: Most positive raw funding rate first
3. **Tertiary Ranking**: 5-day average volume (final tie-breaker)
   - **Both Sides**: Highest volume first

## 4.4 Top 5 Selection
- **Long Positions**: Select top 5 most negative adjusted annualized funding rates
- **Short Positions**: Select top 5 most positive adjusted annualized funding rates
- **Fixed Count**: Always exactly 5 positions per leg (no minimum/maximum logic)

## 4.5 Selection Constraints
- **Fixed Positions**: Exactly 5 positions per leg
- **No EV Threshold**: Select top 5 regardless of magnitude
- **No Balance Requirement**: No complex balancing between legs

# 5. POSITION SIZING

## 5.1 Equal Dollar Weighting
**Base Approach**: Start with equal dollar weights (1/N)
- **Equal Weight Calculation**: `equal_weight = 1.0 / num_positions`

## 5.2 Volatility Targeting
**Target Volatility**: 30% annualized (0.30)
**Volatility Adjustment**:
- Calculate leverage for each position: `leverage = target_vol / position_volatility`
- Position size: `equal_weight × leverage × total_capital`

## 5.3 Position Limits and Constraints
- **Maximum Position Size**: 25% of total capital per position
- **Contract Compliance**: Round to exchange-specific lot sizes and minimum order sizes
- **Price Validation**: Use exchange-specific price tick sizes

## 5.4 Leg Balancing
**Dollar Volatility Balancing**:
- Calculate dollar volatility for each leg: `position_size × volatility`
- Balance legs to achieve equal dollar volatility between long and short sides
- Scaling factors applied to maintain market neutrality

## 5.5 Beta Projection
- **Status**: DISABLED for this strategy
- **Rationale**: Strategy focuses on funding rate arbitrage without market beta neutrality
- **Implementation**: No beta calculations, monitoring, or optimization performed

# 6. PORTFOLIO RISK MANAGEMENT

## 6.1 Portfolio EV Threshold
**EV Calculation**:
- Long leg EV: `sum(adjusted_funding × -1)` for all long positions
- Short leg EV: `sum(adjusted_funding)` for all short positions
- Portfolio EV: `long_leg_EV + short_leg_EV`

**Trading Cost Threshold**:
- Get exchange-specific trading cost (10.95% annualized)
- If portfolio EV < trading cost threshold: close all positions
- Prevents trading when expected costs exceed expected returns

## 6.2 Position Buffer Zones
**Buffer Zone Logic**:
- Calculate buffer around each target position: `target_size × 5%`
- **No-Trade Zone**: If current position within [target-buffer, target+buffer], no action
- **Trade Up**: If current < target-buffer, trade up to target-buffer
- **Trade Down**: If current > target+buffer, trade down to target+buffer

**Position Closure**:
- Close positions not in target list if above minimum threshold ($10 USD)
- Prevents closing dust positions

# 7. EXECUTION ENGINE

## 7.1 Order Execution Strategy
**Post-Only Orders**: Use post-only orders to minimize trading costs
- **Price Calculation**: Use best bid for longs, best ask for shorts
- **Price Offset**: Configurable offset from best bid/ask (default 0%)
- **Retry Logic**: Up to 3 retries for post-only order rejections

## 7.2 Batch Execution
**Randomized Batching**:
- **Batch Size**: Random 1-5 orders per batch
- **Batch Intervals**: Random 30-300 seconds between batches
- **Orderbook Levels**: Split orders across 5-10 orderbook levels
- **Asynchronous**: Each coin executes independently

## 7.3 Delta Reconciliation
**Position Tolerance**: 5% of individual position value
- Continue execution until all position deltas within tolerance
- **Maximum Iterations**: 10 iterations before execution fails
- **Settlement Time**: 5 minutes wait for orders to settle before reconciliation

## 7.4 Error Handling
**Order Failures**:
- **Post-Only Rejections**: Retry with safer maker prices
- **Fallback**: Use taker orders if post-only repeatedly fails
- **Rate Limiting**: Adaptive delays and throttling

**Exchange Errors**:
- **Circuit Breaker**: Stop execution after 5 consecutive failures
- **Retry Logic**: Exponential backoff with jitter
- **Graceful Degradation**: Skip problematic symbols, continue with others

# 8. MONITORING AND REBALANCING

## 8.1 Rebalancing Schedule
**Daily Rebalancing**: 23:00 UTC (1 hour before funding payments)
- **Funding Times**: UTC 00:00, 08:00, 16:00
- **Monitoring**: Additional checks at 07:00 and 15:00 UTC

## 8.2 Position Monitoring
**Active Order Monitoring**:
- Check for unfilled orders and position alignment
- **Monitoring Windows**: 1 hour before each funding time
- **Reconciliation**: Ensure actual positions match target positions

## 8.3 Performance Tracking
**Strategy Attribution**:
- Track individual position P&L
- Monitor funding payments received/paid
- Calculate strategy-specific performance metrics

**Risk Monitoring**:
- Portfolio beta tracking
- Volatility realization vs target
- Position concentration limits

## 8.4 Alerts and Notifications
**Funding Rate Alerts**: Alert on extreme funding rate changes (>0.5%)
**Execution Alerts**: Alert on execution failures or significant slippage
**Risk Alerts**: Alert on portfolio beta deviation or volatility breaches

# 9. CONFIGURATION PARAMETERS

## 9.1 Universe Selection
```yaml
min_daily_volume_usd: 1000000      # $1M minimum volume
min_volatility_threshold: 0.05     # 5% minimum volatility
exclude_new_listings_days: 60      # Exclude new listings
min_historical_data_days: 60       # Minimum data history
```

## 9.2 Feature Engineering
```yaml
funding_rate_lookback_days: 3      # 3-day funding rate average
trading_cost_adjustment_annualized: 0.1095  # 10.95% annualized cost
volatility_weights:
  vol_60d: 0.3                     # 60-day volatility weight
  vol_30d: 0.5                     # 30-day volatility weight
  vol_10d: 0.2                     # 10-day volatility weight
```

## 9.3 Position Selection
```yaml
max_positions_per_leg: 5           # Fixed 5 positions per leg
```

## 9.4 Position Sizing
```yaml
target_volatility: 0.30            # 30% target volatility
max_position_capital_pct: 25       # 25% max per position
enable_beta_projection: false      # Disable beta neutrality for this strategy
```

## 9.5 Risk Management
```yaml
buffer_zone_tolerance_percentage: 5.0    # 5% position buffer
min_close_threshold_usd: 10.0           # $10 minimum close threshold
delta_tolerance_percentage: 5.0         # 5% of position value tolerance
```

## 9.6 Execution
```yaml
post_only_max_retries: 3               # Post-only retry limit
order_price_offset_pct: 0.0            # Price offset from best bid/ask
max_execution_iterations: 10           # Maximum execution iterations
order_settle_time_seconds: 300         # 5 minutes settlement time
```
