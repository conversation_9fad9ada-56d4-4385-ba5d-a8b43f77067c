"""
Position management and sizing calculations
"""

import logging
from typing import Dict, List, Optional, Any, Tuple

from exchanges.base import ExchangeInterface
from data.analyzer import DataAnalyzer
from utils.data_validation import DataUnitValidator
from utils.contract_specs import contract_spec_manager
from execution.beta_optimizer import BetaOptimizer

logger = logging.getLogger(__name__)


class PositionManager:
    """Manages position sizing and portfolio calculations"""
    
    def __init__(self, exchange: ExchangeInterface, analyzer: DataAnalyzer, config: Dict[str, Any]):
        self.exchange = exchange
        self.analyzer = analyzer
        self.config = config

        # Initialize beta optimizer if beta projection is enabled
        if config.get('enable_beta_projection', False):
            self.beta_optimizer = BetaOptimizer(config)
            logger.info("🔧 Beta projection enabled - initialized BetaOptimizer")
        else:
            self.beta_optimizer = None
            logger.info("🔧 Beta projection disabled")
    
    async def calculate_position_sizes_ev_based(self, long_candidates: List[Dict], short_candidates: List[Dict]) -> List[Dict]:
        """
        Calculate position sizes with equal dollar weighting and equal dollar volatility between legs.

        Key features:
        1. Use equal dollar weights (1/N) for position allocation
        2. Ensure equal dollar volatility between long and short legs
        3. Apply maximum capital per coin constraint (25%)
        4. Target volatility normalization for individual coins
        5. Fixed position counts (top 5 per leg)
        """
        if not long_candidates and not short_candidates:
            return []

        logger.info("📊 Calculating position sizes with equal dollar weighting...")

        # Get configuration parameters
        total_capital = self.config['total_capital_usd']
        target_vol = self.config.get('target_volatility', 0.30)  # 30% default target volatility (standardized)
        max_position_capital_pct = self.config.get('max_position_capital_pct', 25)  # 25% max per coin
        max_position_size = total_capital * (max_position_capital_pct / 100)

        logger.info(f"📊 Total capital: ${total_capital:,.0f}")
        logger.info(f"📊 Target volatility: {target_vol*100:.1f}%")
        logger.info(f"📊 Max position size: ${max_position_size:,.0f} ({max_position_capital_pct}%)")
        logger.info(f"📊 Available candidates: {len(long_candidates)} longs, {len(short_candidates)} shorts")

        # Select final viable positions from candidates
        final_long_positions = await self._select_all_viable_positions(long_candidates, 'long')
        final_short_positions = await self._select_all_viable_positions(short_candidates, 'short')

        if not final_long_positions and not final_short_positions:
            logger.warning("⚠️ No viable positions found after filtering")
            return []

        # Calculate equal-weighted position sizes for each leg (1/N)
        long_positions_with_sizes = []
        short_positions_with_sizes = []

        if final_long_positions:
            long_positions_with_sizes = await self._calculate_equal_weighted_positions(
                final_long_positions, 'long', target_vol, max_position_size)

        if final_short_positions:
            short_positions_with_sizes = await self._calculate_equal_weighted_positions(
                final_short_positions, 'short', target_vol, max_position_size)

        # Calculate dollar volatility for each leg
        long_dollar_vol = self._calculate_leg_dollar_volatility(long_positions_with_sizes)
        short_dollar_vol = self._calculate_leg_dollar_volatility(short_positions_with_sizes)

        # Adjust position sizes to achieve equal dollar volatility between legs
        adjusted_positions = self._balance_leg_volatilities(
            long_positions_with_sizes, short_positions_with_sizes,
            long_dollar_vol, short_dollar_vol)

        # Apply beta projection if enabled
        if self.beta_optimizer and adjusted_positions:
            adjusted_positions = await self._apply_beta_projection(adjusted_positions)

        # Calculate portfolio EV and check trading cost threshold
        portfolio_ev = self._calculate_portfolio_ev(adjusted_positions)
        trading_cost_threshold = self._get_trading_cost_threshold()

        logger.info(f"📊 Portfolio EV Analysis:")
        logger.info(f"   Portfolio EV: {portfolio_ev:.6f}")
        logger.info(f"   Trading cost threshold: {trading_cost_threshold:.6f}")

        # If portfolio EV is below trading cost threshold, return empty portfolio
        if portfolio_ev < trading_cost_threshold:
            logger.warning(f"⚠️ Portfolio EV ({portfolio_ev:.6f}) below trading cost threshold ({trading_cost_threshold:.6f})")
            logger.warning(f"⚠️ Returning empty portfolio - will close all existing positions")
            return []

        # Log portfolio summary
        self._log_portfolio_summary(adjusted_positions, total_capital)

        return adjusted_positions

    async def calculate_position_sizes(self, long_candidates: List[Dict], short_candidates: List[Dict], target_position_count: int) -> List[Dict]:
        """Calculate volatility-targeted position sizes with true volatility-based market neutrality"""
        if not long_candidates and not short_candidates:
            return []
        
        logger.info("📊 Calculating volatility-targeted position sizes with risk-neutral allocation...")
        
        # Get target volatility from config
        target_vol = self.config.get('target_volatility', 0.15)  # 15% default target volatility
        total_capital = self.config['total_capital_usd']

        # Use the target position count passed from analyzer (not the buffer count)
        position_count = target_position_count

        logger.info(f"📊 Target positions: {position_count} longs + {position_count} shorts")
        logger.info(f"📊 Available candidates: {len(long_candidates)} longs, {len(short_candidates)} shorts")

        # Select final positions from candidates, skipping problematic symbols
        final_long_positions = await self._select_final_positions(long_candidates, position_count, 'long')
        final_short_positions = await self._select_final_positions(short_candidates, position_count, 'short')
        
        # Calculate volatility-adjusted weights for each leg separately (NO normalization to sum=1)
        long_weights = []
        long_avg_vol = 0
        if final_long_positions:
            long_weights, long_avg_vol = await self._calculate_vol_adjusted_weights_unnormalized(final_long_positions, target_vol)
        
        short_weights = []
        short_avg_vol = 0
        if final_short_positions:
            short_weights, short_avg_vol = await self._calculate_vol_adjusted_weights_unnormalized(final_short_positions, target_vol)
        
        # Calculate risk-neutral capital allocation based on volatility matching
        long_leg_capital, short_leg_capital = self._calculate_risk_neutral_allocation(
            total_capital, long_avg_vol, short_avg_vol)
        
        target_positions = []
        
        # Create long positions with volatility-adjusted sizes
        if final_long_positions and long_weights:
            for i, coin in enumerate(final_long_positions):
                weight = long_weights[i]
                position_size_usd = long_leg_capital * weight
                
                # Apply max position size limit if specified
                max_position_size = self.config.get('max_position_size_usd')
                if max_position_size and position_size_usd > max_position_size:
                    position_size_usd = max_position_size
                    logger.warning(f"⚠️ {coin['symbol']} long position capped at ${max_position_size}")
                
                # CRITICAL: Calculate position size with contract specification rounding
                # This ensures minimum order sizes and lot size compliance
                size_native, actual_position_usd = contract_spec_manager.calculate_position_size_with_rounding(
                    coin['symbol'], position_size_usd, float(coin['price']))

                # Validate position size calculations
                if not DataUnitValidator.validate_position_size(coin['symbol'], actual_position_usd, size_native, float(coin['price'])):
                    logger.error(f"❌ Position size validation failed for {coin['symbol']}")
                    continue

                # Round price for validation (use the same rounded price from position sizing)
                rounded_price = contract_spec_manager.round_price(coin['symbol'], float(coin['price']), round_up=False)

                # Validate contract specifications using rounded values
                is_valid, error_msg = contract_spec_manager.validate_order_specs(
                    coin['symbol'], size_native, rounded_price)

                if not is_valid:
                    logger.error(f"❌ Contract spec validation failed for {coin['symbol']}: {error_msg}")
                    continue

                # Log position size details for verification
                logger.debug(f"📊 {coin['symbol']} LONG: target=${position_size_usd:.2f}, "
                           f"actual=${actual_position_usd:.2f}, qty={size_native:.6f} {coin.get('base', 'BASE')} "
                           f"@ ${float(coin['price']):.2f}")

                # Update position size to actual rounded amount
                position_size_usd = actual_position_usd

                target_positions.append({
                    'symbol': coin['symbol'],
                    'side': 'long',
                    'size_usd': position_size_usd,
                    'size_native': size_native,  # In base currency (BTC for BTCUSDT)
                    'adjusted_funding': coin['adjusted_funding'],  # Fix: Use consistent field name
                    'weight': weight,
                    'volatility': coin.get('weighted_volatility', 0),
                    'leverage': coin.get('leverage', 1)
                })
        
        # Create short positions with volatility-adjusted sizes
        if final_short_positions and short_weights:
            for i, coin in enumerate(final_short_positions):
                weight = short_weights[i]
                position_size_usd = short_leg_capital * weight
                
                # Apply max position size limit if specified
                max_position_size = self.config.get('max_position_size_usd')
                if max_position_size and position_size_usd > max_position_size:
                    position_size_usd = max_position_size
                    logger.warning(f"⚠️ {coin['symbol']} short position capped at ${max_position_size}")
                
                # CRITICAL: Calculate position size with contract specification rounding
                # This ensures minimum order sizes and lot size compliance
                size_native, actual_position_usd = contract_spec_manager.calculate_position_size_with_rounding(
                    coin['symbol'], position_size_usd, float(coin['price']))

                # Validate position size calculations
                if not DataUnitValidator.validate_position_size(coin['symbol'], actual_position_usd, size_native, float(coin['price'])):
                    logger.error(f"❌ Position size validation failed for {coin['symbol']}")
                    continue

                # Round price for validation (use the same rounded price from position sizing)
                rounded_price = contract_spec_manager.round_price(coin['symbol'], float(coin['price']), round_up=False)

                # Validate contract specifications using rounded values
                is_valid, error_msg = contract_spec_manager.validate_order_specs(
                    coin['symbol'], size_native, rounded_price)

                if not is_valid:
                    logger.error(f"❌ Contract spec validation failed for {coin['symbol']}: {error_msg}")
                    continue

                # Log position size details for verification
                logger.debug(f"📊 {coin['symbol']} SHORT: target=${position_size_usd:.2f}, "
                           f"actual=${actual_position_usd:.2f}, qty={size_native:.6f} {coin.get('base', 'BASE')} "
                           f"@ ${float(coin['price']):.2f}")

                # Update position size to actual rounded amount
                position_size_usd = actual_position_usd

                target_positions.append({
                    'symbol': coin['symbol'],
                    'side': 'short',
                    'size_usd': position_size_usd,
                    'size_native': size_native,  # In base currency (BTC for BTCUSDT)
                    'adjusted_funding': coin['adjusted_funding'],  # Fix: Use consistent field name
                    'weight': weight,
                    'volatility': coin.get('weighted_volatility', 0),
                    'leverage': coin.get('leverage', 1)
                })
        
        # Calculate actual totals and risk metrics
        long_total = sum(pos['size_usd'] for pos in target_positions if pos['side'] == 'long')
        short_total = sum(pos['size_usd'] for pos in target_positions if pos['side'] == 'short')
        long_risk = long_total * long_avg_vol if long_avg_vol > 0 else 0
        short_risk = short_total * short_avg_vol if short_avg_vol > 0 else 0
        total_allocated = long_total + short_total
        
        logger.info(f"💰 Volatility-Based Risk-Neutral Allocation:")
        logger.info(f"   Target volatility: {target_vol*100:.1f}%")
        logger.info(f"   Long leg:  ${long_total:.0f} (avg vol: {long_avg_vol*100:.1f}%) = ${long_risk:.0f} vol-dollars")
        logger.info(f"   Short leg: ${short_total:.0f} (avg vol: {short_avg_vol*100:.1f}%) = ${short_risk:.0f} vol-dollars")
        logger.info(f"   Risk neutrality: ${abs(long_risk - short_risk):.0f} vol-dollar difference")
        logger.info(f"   Capital usage: ${total_allocated:.0f} / ${total_capital:.0f} ({total_allocated/total_capital*100:.1f}%)")
        
        # Log individual position details
        for pos in target_positions:
            logger.info(f"   {pos['symbol']}: {pos['side']} ${pos['size_usd']:.0f} "
                       f"(weight: {pos['weight']:.3f}, vol: {pos['volatility']*100:.1f}%, "
                       f"leverage: {pos['leverage']:.2f}x)")

        # Calculate portfolio EV and check trading cost threshold
        portfolio_ev = self._calculate_portfolio_ev(target_positions)
        trading_cost_threshold = self._get_trading_cost_threshold()

        logger.info(f"📊 Portfolio EV Analysis:")
        logger.info(f"   Portfolio EV: {portfolio_ev:.6f}")
        logger.info(f"   Trading cost threshold: {trading_cost_threshold:.6f}")

        # If portfolio EV is below trading cost threshold, return empty portfolio
        if portfolio_ev < trading_cost_threshold:
            logger.warning(f"⚠️ Portfolio EV ({portfolio_ev:.6f}) below trading cost threshold ({trading_cost_threshold:.6f})")
            logger.warning(f"⚠️ Returning empty portfolio - will close all existing positions")
            return []

        return target_positions
    
    async def _select_final_positions(self, candidates: List[Dict], target_count: int, side: str) -> List[Dict]:
        """Select final positions from candidates, skipping problematic symbols"""
        if not candidates:
            return []
        
        logger.info(f"🔍 Selecting {target_count} final {side} positions from {len(candidates)} candidates...")
        
        final_positions = []
        skipped_symbols = []
        
        for i, candidate in enumerate(candidates):
            if len(final_positions) >= target_count:
                break  # We have enough positions
            
            symbol = candidate['symbol']
            
            try:
                # Test if this symbol is viable for trading
                is_viable = await self._test_symbol_viability(symbol)
                
                if is_viable:
                    final_positions.append(candidate)
                    logger.debug(f"✅ {symbol} selected for {side} position (rank {i+1})")
                else:
                    skipped_symbols.append(symbol)
                    logger.warning(f"⏭️ Skipping {symbol} - failed viability test (rank {i+1})")
            
            except Exception as e:
                skipped_symbols.append(symbol)
                logger.warning(f"⏭️ Skipping {symbol} - error during viability test: {e}")
        
        # Log selection results
        logger.info(f"📊 {side.capitalize()} position selection results:")
        logger.info(f"   ✅ Selected: {len(final_positions)} positions")
        logger.info(f"   ⏭️ Skipped: {len(skipped_symbols)} symbols")
        
        if skipped_symbols:
            logger.info(f"   Skipped symbols: {', '.join(skipped_symbols)}")
        
        if len(final_positions) < target_count:
            logger.warning(f"⚠️ Only found {len(final_positions)} viable {side} positions (target: {target_count})")
        
        return final_positions
    
    async def _test_symbol_viability(self, symbol: str) -> bool:
        """Test if a symbol is viable for trading (orderbook, pricing, etc.)"""
        try:
            # Test 1: Fetch orderbook to ensure it's available and has data
            orderbook = await self.exchange.fetch_order_book(symbol)
            
            if not self._validate_orderbook(orderbook, symbol):
                logger.debug(f"❌ {symbol} failed orderbook validation")
                return False
            
            # Test 2: Check if we can calculate limit prices for both sides
            try:
                buy_price = self._calculate_limit_price_safe(orderbook, 'buy')
                sell_price = self._calculate_limit_price_safe(orderbook, 'sell')
                
                if buy_price is None or sell_price is None:
                    logger.debug(f"❌ {symbol} failed price calculation")
                    return False
                
                # Test 3: Check spread reasonableness
                spread_pct = (sell_price - buy_price) / buy_price
                max_spread = self.config.get('max_spread_threshold', 0.05)  # 5% default
                
                if spread_pct > max_spread:
                    logger.debug(f"❌ {symbol} spread too wide: {spread_pct*100:.2f}%")
                    return False
            
            except Exception as price_error:
                logger.debug(f"❌ {symbol} price calculation failed: {price_error}")
                return False
            
            # Test 4: Verify we can fetch ticker data
            try:
                ticker = await self.exchange.fetch_ticker(symbol)
                if not ticker or 'last' not in ticker or ticker['last'] is None:
                    logger.debug(f"❌ {symbol} ticker data unavailable")
                    return False
            except Exception as ticker_error:
                logger.debug(f"❌ {symbol} ticker fetch failed: {ticker_error}")
                return False
            
            return True
            
        except Exception as e:
            logger.debug(f"❌ {symbol} viability test failed: {e}")
            return False
    
    def _validate_orderbook(self, orderbook: Dict, symbol: str) -> bool:
        """Validate orderbook has sufficient depth"""
        try:
            bids = orderbook.get('bids', [])
            asks = orderbook.get('asks', [])
            
            min_depth = self.config.get('min_orderbook_depth', 3)
            
            if len(bids) < min_depth or len(asks) < min_depth:
                logger.debug(f"❌ {symbol} insufficient orderbook depth")
                return False
            
            # Check if prices are reasonable (not zero or negative)
            if not bids[0][0] > 0 or not asks[0][0] > 0:
                logger.debug(f"❌ {symbol} invalid orderbook prices")
                return False
            
            return True
            
        except Exception as e:
            logger.debug(f"❌ {symbol} orderbook validation failed: {e}")
            return False
    
    def _calculate_limit_price_safe(self, orderbook: Dict, side: str) -> Optional[float]:
        """Safely calculate limit price from orderbook"""
        try:
            if side == 'buy':
                # For buying, use ask prices (we pay the ask)
                asks = orderbook.get('asks', [])
                if not asks:
                    return None
                return asks[0][0]  # Best ask price
            else:
                # For selling, use bid prices (we receive the bid)
                bids = orderbook.get('bids', [])
                if not bids:
                    return None
                return bids[0][0]  # Best bid price
                
        except Exception as e:
            logger.debug(f"❌ Error calculating limit price: {e}")
            return None
    
    async def _calculate_vol_adjusted_weights_unnormalized(self, positions: List[Dict], target_vol: float) -> Tuple[List[float], float]:
        """Calculate volatility-adjusted weights WITHOUT normalizing to sum=1"""
        if not positions:
            return [], 0.0
        
        # Calculate individual leverage for each position
        leverages = []
        volatilities = []
        
        for coin in positions:
            vol = coin.get('weighted_volatility', 0.5)
            leverage = target_vol / vol if vol > 0 else 1.0

            leverages.append(leverage)
            volatilities.append(vol)
            coin['leverage'] = leverage  # Store for later use
        
        # Weights are proportional to leverage (NO normalization)
        total_leverage = sum(leverages)
        weights = [lev / total_leverage for lev in leverages]
        
        # Calculate average volatility weighted by position sizes
        avg_vol = sum(w * v for w, v in zip(weights, volatilities))
        
        return weights, avg_vol
    
    def _calculate_risk_neutral_allocation(self, total_capital: float, long_avg_vol: float, short_avg_vol: float) -> Tuple[float, float]:
        """Calculate risk-neutral capital allocation between long and short legs"""
        if long_avg_vol <= 0 and short_avg_vol <= 0:
            # Equal allocation if no volatility data
            return total_capital / 2, total_capital / 2
        elif long_avg_vol <= 0:
            # Only short positions have volatility data
            return 0, total_capital
        elif short_avg_vol <= 0:
            # Only long positions have volatility data
            return total_capital, 0
        
        # Risk-neutral allocation: allocate capital inversely proportional to volatility
        # This ensures long_capital * long_vol ≈ short_capital * short_vol
        vol_ratio = short_avg_vol / long_avg_vol
        
        # Calculate allocation
        long_leg_capital = total_capital * vol_ratio / (1 + vol_ratio)
        short_leg_capital = total_capital - long_leg_capital
        
        return long_leg_capital, short_leg_capital

    async def _apply_beta_projection(self, positions: List[Dict]) -> List[Dict]:
        """
        Apply beta projection to achieve beta-neutral portfolio while minimizing weight changes.

        This is the core beta projection implementation that:
        1. Extracts beta values for each position
        2. Uses constrained optimization to achieve beta neutrality
        3. Updates position sizes based on optimized weights
        4. Maintains contract specification compliance

        Args:
            positions: List of position dictionaries with volatility-targeted weights

        Returns:
            List of position dictionaries with beta-neutral weights
        """
        try:
            if not positions:
                return positions

            logger.info("🎯 Applying beta projection for portfolio beta neutrality...")

            # Extract beta values for each position
            betas = []
            for position in positions:
                symbol = position['symbol']

                # Get beta from the position data (should be enriched earlier)
                beta = position.get('beta')
                if beta is None:
                    # Fallback: calculate beta if not available
                    if self.analyzer.beta_calculator:
                        beta = await self.analyzer.beta_calculator.calculate_beta(symbol)
                    if beta is None:
                        beta = self.config.get('default_beta', 1.0)
                        logger.warning(f"⚠️ Using default beta {beta} for {symbol}")

                betas.append(beta)
                position['beta'] = beta  # Ensure beta is stored in position

            # Calculate current portfolio beta
            total_weight = sum(abs(pos.get('weight', pos['size_usd'] / self.config['total_capital_usd'])) for pos in positions)
            if total_weight == 0:
                logger.warning("⚠️ Total portfolio weight is zero, skipping beta projection")
                return positions

            # Normalize weights for beta calculation
            normalized_positions = []
            for i, position in enumerate(positions):
                pos_copy = position.copy()
                # Use existing weight or calculate from position size
                weight = pos_copy.get('weight', pos_copy['size_usd'] / self.config['total_capital_usd'])
                pos_copy['weight'] = weight / total_weight  # Normalize to sum = 1
                normalized_positions.append(pos_copy)

            # Apply beta optimization
            optimized_positions = self.beta_optimizer.optimize_weights_for_beta_neutrality(
                normalized_positions, betas)

            if optimized_positions is None:
                logger.warning("⚠️ Beta optimization failed, using original positions")
                return positions

            # Update position sizes based on optimized weights
            updated_positions = []
            total_capital = self.config['total_capital_usd']

            for optimized_pos in optimized_positions:
                # Calculate new position size based on optimized weight
                new_weight = optimized_pos['weight']
                new_size_usd = new_weight * total_capital

                # Recalculate contract-compliant position size
                symbol = optimized_pos['symbol']
                price = float(optimized_pos.get('price', 0))
                if price == 0:
                    # Try to get price from original position
                    original_pos = next((p for p in positions if p['symbol'] == symbol), None)
                    if original_pos and 'price' in original_pos:
                        price = float(original_pos['price'])
                    else:
                        logger.error(f"❌ No price available for {symbol}, skipping beta projection update")
                        continue

                # Calculate contract-compliant position size
                size_native, actual_size_usd = contract_spec_manager.calculate_position_size_with_rounding(
                    symbol, new_size_usd, price)

                # Validate position size
                if not DataUnitValidator.validate_position_size(symbol, actual_size_usd, size_native, price):
                    logger.error(f"❌ Beta-projected position size validation failed for {symbol}")
                    continue

                # Update position with beta-projected values
                updated_pos = optimized_pos.copy()
                updated_pos['size_usd'] = actual_size_usd
                updated_pos['size_native'] = size_native
                updated_pos['weight'] = new_weight

                # Log weight change
                original_weight = optimized_pos.get('original_weight', 0)
                weight_change = optimized_pos.get('weight_change', 0)
                if abs(weight_change) > 0.01:  # Only log significant changes
                    logger.info(f"🎯 {symbol}: weight {original_weight:.4f} → {new_weight:.4f} "
                              f"(Δ{weight_change:+.4f}, β={optimized_pos['beta']:.4f})")

                updated_positions.append(updated_pos)

            # Calculate final portfolio beta for verification
            if self.analyzer.beta_calculator:
                final_portfolio_beta = await self.analyzer.beta_calculator.calculate_portfolio_beta(updated_positions)
                logger.info(f"✅ Beta projection completed - final portfolio beta: {final_portfolio_beta:.4f}")

            return updated_positions

        except Exception as e:
            logger.error(f"❌ Error in beta projection: {e}")
            logger.warning("⚠️ Using original positions without beta projection")
            return positions

    async def _select_all_viable_positions(self, candidates: List[Dict], side: str) -> List[Dict]:
        """Select all viable positions from candidates (no count limit for EV-based approach)"""
        if not candidates:
            return []

        logger.info(f"🔍 Testing viability of {len(candidates)} {side} candidates...")

        viable_positions = []
        skipped_symbols = []

        for i, candidate in enumerate(candidates):
            symbol = candidate['symbol']

            try:
                # Test if this symbol is viable for trading
                is_viable = await self._test_symbol_viability(symbol)

                if is_viable:
                    viable_positions.append(candidate)
                    logger.debug(f"✅ {symbol} viable for {side} position (rank {i+1})")
                else:
                    skipped_symbols.append(symbol)
                    logger.warning(f"⏭️ Skipping {symbol} - failed viability test (rank {i+1})")

            except Exception as e:
                skipped_symbols.append(symbol)
                logger.warning(f"⏭️ Skipping {symbol} - error during viability test: {e}")

        logger.info(f"✅ Selected {len(viable_positions)} viable {side} positions")
        if skipped_symbols:
            logger.info(f"⏭️ Skipped {len(skipped_symbols)} {side} symbols: {', '.join(skipped_symbols[:5])}{'...' if len(skipped_symbols) > 5 else ''}")

        return viable_positions

    async def _calculate_equal_weighted_positions(self, positions: List[Dict], side: str, target_vol: float, max_position_size: float) -> List[Dict]:
        """Calculate position sizes using equal dollar weights (1/N) and target volatility"""
        if not positions:
            return []

        logger.debug(f"📊 Calculating equal-weighted {side} position sizes...")

        # Calculate equal weight for each position (1/N)
        num_positions = len(positions)
        equal_weight = 1.0 / num_positions

        sized_positions = []

        for coin in positions:
            volatility = coin.get('weighted_volatility', 0.5)

            # Start with equal dollar weight
            base_weight = equal_weight

            # Apply volatility targeting: adjust size to achieve target volatility
            # Higher volatility coins get smaller positions to maintain target vol
            vol_adjustment = target_vol / volatility if volatility > 0 else 1.0

            # Calculate position size (base weight * volatility adjustment)
            position_size_usd = base_weight * vol_adjustment * self.config['total_capital_usd']

            # Apply maximum position size constraint
            if position_size_usd > max_position_size:
                position_size_usd = max_position_size
                logger.debug(f"⚠️ {coin['symbol']} {side} position capped at ${max_position_size:,.0f}")

            # Calculate contract-compliant position size
            size_native, actual_position_usd = contract_spec_manager.calculate_position_size_with_rounding(
                coin['symbol'], position_size_usd, float(coin['price']))

            # Validate position size calculations
            if not DataUnitValidator.validate_position_size(coin['symbol'], actual_position_usd, size_native, float(coin['price'])):
                logger.error(f"❌ Position size validation failed for {coin['symbol']}")
                continue

            # Validate contract specifications
            rounded_price = contract_spec_manager.round_price(coin['symbol'], float(coin['price']), round_up=False)
            is_valid, error_msg = contract_spec_manager.validate_order_specs(coin['symbol'], size_native, rounded_price)

            if not is_valid:
                logger.error(f"❌ Contract spec validation failed for {coin['symbol']}: {error_msg}")
                continue

            # Store position with all relevant data
            sized_position = {
                'symbol': coin['symbol'],
                'side': side,
                'size_usd': actual_position_usd,
                'size_native': size_native,
                'funding_rate': coin['adjusted_funding'],
                'equal_weight': equal_weight,
                'base_weight': base_weight,
                'volatility': volatility,
                'vol_adjustment': vol_adjustment,
                'dollar_volatility': actual_position_usd * volatility  # For leg balancing
            }

            sized_positions.append(sized_position)

            logger.debug(f"📊 {coin['symbol']} {side.upper()}: ${actual_position_usd:,.0f} "
                        f"(equal weight: {equal_weight:.4f}, vol: {volatility*100:.1f}%, adj: {vol_adjustment:.2f}x)")

        return sized_positions

    def _calculate_leg_dollar_volatility(self, positions: List[Dict]) -> float:
        """Calculate total dollar volatility for a leg (long or short)"""
        if not positions:
            return 0.0

        total_dollar_vol = sum(pos['dollar_volatility'] for pos in positions)
        return total_dollar_vol

    def _balance_leg_volatilities(self, long_positions: List[Dict], short_positions: List[Dict],
                                 long_dollar_vol: float, short_dollar_vol: float) -> List[Dict]:
        """
        Balance position sizes to achieve equal dollar volatility between legs.

        This ensures market neutrality while maintaining EV-based weighting within each leg.
        """
        if not long_positions and not short_positions:
            return []

        logger.info(f"📊 Balancing leg volatilities:")
        logger.info(f"   Long leg dollar volatility: ${long_dollar_vol:,.0f}")
        logger.info(f"   Short leg dollar volatility: ${short_dollar_vol:,.0f}")

        # If only one leg exists, use all available capital
        if not long_positions:
            logger.info("   Only short positions available - using full capital")
            return short_positions
        elif not short_positions:
            logger.info("   Only long positions available - using full capital")
            return long_positions

        # Calculate target dollar volatility (average of both legs)
        target_dollar_vol = (long_dollar_vol + short_dollar_vol) / 2

        # Calculate scaling factors for each leg
        long_scale = target_dollar_vol / long_dollar_vol if long_dollar_vol > 0 else 1.0
        short_scale = target_dollar_vol / short_dollar_vol if short_dollar_vol > 0 else 1.0

        logger.info(f"   Target dollar volatility per leg: ${target_dollar_vol:,.0f}")
        logger.info(f"   Long leg scaling factor: {long_scale:.3f}")
        logger.info(f"   Short leg scaling factor: {short_scale:.3f}")

        # Apply scaling to positions
        balanced_positions = []

        # Scale long positions
        for pos in long_positions:
            scaled_pos = pos.copy()
            scaled_pos['size_usd'] *= long_scale
            scaled_pos['dollar_volatility'] *= long_scale

            # Recalculate native size with new USD amount
            size_native, actual_usd = contract_spec_manager.calculate_position_size_with_rounding(
                pos['symbol'], scaled_pos['size_usd'], float(pos.get('price', 1)))

            scaled_pos['size_usd'] = actual_usd
            scaled_pos['size_native'] = size_native
            scaled_pos['dollar_volatility'] = actual_usd * pos['volatility']

            balanced_positions.append(scaled_pos)

        # Scale short positions
        for pos in short_positions:
            scaled_pos = pos.copy()
            scaled_pos['size_usd'] *= short_scale
            scaled_pos['dollar_volatility'] *= short_scale

            # Recalculate native size with new USD amount
            size_native, actual_usd = contract_spec_manager.calculate_position_size_with_rounding(
                pos['symbol'], scaled_pos['size_usd'], float(pos.get('price', 1)))

            scaled_pos['size_usd'] = actual_usd
            scaled_pos['size_native'] = size_native
            scaled_pos['dollar_volatility'] = actual_usd * pos['volatility']

            balanced_positions.append(scaled_pos)

        # Verify final balance
        final_long_vol = sum(pos['dollar_volatility'] for pos in balanced_positions if pos['side'] == 'long')
        final_short_vol = sum(pos['dollar_volatility'] for pos in balanced_positions if pos['side'] == 'short')

        logger.info(f"   Final long leg dollar volatility: ${final_long_vol:,.0f}")
        logger.info(f"   Final short leg dollar volatility: ${final_short_vol:,.0f}")
        logger.info(f"   Volatility balance ratio: {final_short_vol/final_long_vol:.3f}" if final_long_vol > 0 else "   No long positions")

        return balanced_positions

    def _log_portfolio_summary(self, positions: List[Dict], total_capital: float):
        """Log comprehensive portfolio summary"""
        if not positions:
            logger.info("📊 Portfolio Summary: No positions")
            return

        long_positions = [p for p in positions if p['side'] == 'long']
        short_positions = [p for p in positions if p['side'] == 'short']

        total_long_usd = sum(p['size_usd'] for p in long_positions)
        total_short_usd = sum(p['size_usd'] for p in short_positions)
        total_deployed = total_long_usd + total_short_usd

        long_dollar_vol = sum(p['dollar_volatility'] for p in long_positions)
        short_dollar_vol = sum(p['dollar_volatility'] for p in short_positions)

        logger.info(f"📊 Equal-Weighted Portfolio Summary:")
        logger.info(f"   Total positions: {len(positions)} ({len(long_positions)} longs, {len(short_positions)} shorts)")
        logger.info(f"   Capital deployed: ${total_deployed:,.0f} / ${total_capital:,.0f} ({total_deployed/total_capital*100:.1f}%)")
        logger.info(f"   Long leg: ${total_long_usd:,.0f} ({total_long_usd/total_deployed*100:.1f}%)")
        logger.info(f"   Short leg: ${total_short_usd:,.0f} ({total_short_usd/total_deployed*100:.1f}%)")
        logger.info(f"   Long dollar volatility: ${long_dollar_vol:,.0f}")
        logger.info(f"   Short dollar volatility: ${short_dollar_vol:,.0f}")
        logger.info(f"   Volatility balance: {short_dollar_vol/long_dollar_vol:.3f}" if long_dollar_vol > 0 else "   No long positions")

        # Log individual position details
        logger.info(f"   Individual positions:")
        for pos in positions:
            equal_weight = pos.get('equal_weight', pos.get('ev_weight', 0))  # Fallback for compatibility
            logger.info(f"     {pos['symbol']}: {pos['side']} ${pos['size_usd']:,.0f} "
                       f"(equal weight: {equal_weight:.4f}, vol: {pos['volatility']*100:.1f}%)")

        return

    def _get_buffer_config(self) -> Dict[str, float]:
        """
        Get all buffer zone related configuration in one place.

        Returns:
            Dictionary with buffer configuration parameters
        """
        return {
            'buffer_tolerance_pct': self.config.get('buffer_zone_tolerance_percentage', 5.0),
            'min_close_threshold_usd': self.config.get('min_close_threshold_usd', 10.0),
            'delta_tolerance_pct': self.config.get('delta_tolerance_percentage', 0.25)
        }

    def _should_close_position(self, symbol: str, current_position: Dict, buffer_config: Dict) -> bool:
        """
        Determine if a position should be closed based on buffer zone logic.

        Args:
            symbol: Trading symbol
            current_position: Current position data
            buffer_config: Buffer configuration parameters

        Returns:
            True if position should be closed, False otherwise
        """
        current_usd = abs(float(current_position.get('size_usd', 0)))
        min_close_threshold = buffer_config['min_close_threshold_usd']

        # Only close positions above minimum threshold to avoid dust trades
        should_close = current_usd > min_close_threshold

        if should_close:
            logger.debug(f"📊 {symbol} closure analysis:")
            logger.debug(f"   Current USD: ${current_usd:,.2f}")
            logger.debug(f"   Min threshold: ${min_close_threshold:,.2f}")
            logger.debug(f"   Decision: CLOSE")
        else:
            logger.debug(f"📊 {symbol} below closure threshold (${current_usd:,.2f} < ${min_close_threshold:,.2f}) - keeping position")

        return should_close

    def _apply_closure_buffer_zones(self, target_symbols: set, current_positions: Dict[str, Dict],
                                  buffer_config: Dict) -> List[Dict]:
        """
        Apply buffer zone logic to positions that need to be closed (not in target list).

        Args:
            target_symbols: Set of symbols in target positions
            current_positions: Dict of current positions
            buffer_config: Buffer configuration parameters

        Returns:
            List of positions that should be closed
        """
        positions_to_close = []

        for symbol in current_positions:
            if symbol not in target_symbols:
                current_pos = current_positions[symbol]

                if self._should_close_position(symbol, current_pos, buffer_config):
                    current_usd = abs(float(current_pos.get('size_usd', 0)))

                    close_position = {
                        'symbol': symbol,
                        'side': 'close',  # Special side to indicate closing
                        'size_usd': 0,
                        'size_native': 0,
                        'price': current_pos.get('price', 0),
                        'ev_weight': 0,
                        'volatility': 0,
                        'leverage': 0,
                        'buffer_action': 'close_position',
                        'current_usd': current_usd,
                        'original_target_usd': 0,
                        'buffer_zone': [0, 0]  # No buffer zone for closures
                    }
                    positions_to_close.append(close_position)
                    logger.info(f"📋 Added {symbol} for closure (current: ${current_usd:,.0f})")

        return positions_to_close

    def apply_position_buffer_zones(self, target_positions: List[Dict], current_positions: Dict[str, Dict]) -> List[Dict]:
        """
        Apply comprehensive buffer zone logic to prevent over-trading.

        This method consolidates ALL buffer zone logic including:
        - Target position buffer zones (trade up/down to buffer bounds)
        - Position closure decisions (for positions not in target list)
        - All buffer-related configuration management

        Buffer zone logic for target positions:
        - If current position (w0) is below w-b, trade up to w-b
        - If current position is above w+b, trade down to w+b
        - If position falls within [w-b, w+b], do nothing (no-trade zone)

        Buffer zone logic for position closures:
        - Only close positions above min_close_threshold_usd
        - Prevents closing tiny/dust positions

        Args:
            target_positions: List of target positions with size_usd
            current_positions: Dict of current positions {symbol: position_data}

        Returns:
            List of all positions that need trading (adjustments + closures)
        """
        # Get centralized buffer configuration
        buffer_config = self._get_buffer_config()
        buffer_tolerance_pct = buffer_config['buffer_tolerance_pct']

        logger.info(f"📊 Applying comprehensive position buffer zones")
        logger.info(f"   Buffer tolerance: {buffer_tolerance_pct}%")
        logger.info(f"   Min close threshold: ${buffer_config['min_close_threshold_usd']:,.0f}")

        # Step 1: Apply buffer zones to target positions
        adjusted_positions = []
        no_trade_count = 0

        for target_pos in target_positions:
            symbol = target_pos['symbol']
            target_usd = target_pos['size_usd']
            side = target_pos['side']

            # Get current position size in USD
            current_pos = current_positions.get(symbol, {})
            current_usd = abs(float(current_pos.get('size_usd', 0)))
            current_side = current_pos.get('side', 'none')

            # If sides don't match, treat current position as 0 (need to close and reopen)
            if current_side != side:
                current_usd = 0

            # Calculate buffer zone around target position (as percentage of target position)
            # Handle edge case: for very small positions, use minimum buffer
            min_buffer_usd = buffer_config['min_close_threshold_usd'] * 0.1  # 10% of min close threshold
            buffer_usd = max(min_buffer_usd, target_usd * (buffer_tolerance_pct / 100))

            lower_bound = max(0, target_usd - buffer_usd)  # Can't go below 0
            upper_bound = target_usd + buffer_usd

            # Validate buffer zone makes sense
            if buffer_usd > target_usd:
                logger.debug(f"⚠️ {symbol}: Buffer zone (${buffer_usd:.2f}) larger than target position (${target_usd:.2f})")
                # For very small positions, use a fixed small buffer instead of percentage
                if target_usd < buffer_config['min_close_threshold_usd']:
                    buffer_usd = min_buffer_usd
                    lower_bound = max(0, target_usd - buffer_usd)
                    upper_bound = target_usd + buffer_usd
                    logger.debug(f"⚠️ {symbol}: Using minimum buffer ${buffer_usd:.2f} for small position")

            # Determine action based on buffer zone logic
            action = "no_trade"
            adjusted_usd = target_usd

            if current_usd < lower_bound:
                # Current position below buffer zone - trade up to lower bound
                action = "trade_up"
                adjusted_usd = lower_bound
            elif current_usd > upper_bound:
                # Current position above buffer zone - trade down to upper bound
                action = "trade_down"
                adjusted_usd = upper_bound
            else:
                # Current position within buffer zone - no trade needed
                action = "no_trade"
                no_trade_count += 1

            # Log buffer zone analysis
            logger.debug(f"📊 {symbol} buffer analysis:")
            logger.debug(f"   Target: ${target_usd:,.0f}, Current: ${current_usd:,.0f}")
            logger.debug(f"   Buffer zone: [${lower_bound:,.0f}, ${upper_bound:,.0f}]")
            logger.debug(f"   Action: {action}, Adjusted: ${adjusted_usd:,.0f}")

            # Only include positions that need trading
            if action != "no_trade":
                adjusted_pos = target_pos.copy()
                adjusted_pos['size_usd'] = adjusted_usd
                adjusted_pos['buffer_action'] = action
                adjusted_pos['original_target_usd'] = target_usd
                adjusted_pos['current_usd'] = current_usd
                adjusted_pos['buffer_zone'] = [lower_bound, upper_bound]

                # Recalculate native size with adjusted USD amount
                try:
                    from utils.contract_specs import contract_spec_manager
                    price = float(target_pos.get('price', 1))
                    size_native, actual_usd = contract_spec_manager.calculate_position_size_with_rounding(
                        symbol, adjusted_usd, price)

                    adjusted_pos['size_usd'] = actual_usd
                    adjusted_pos['size_native'] = size_native
                except Exception as e:
                    logger.warning(f"⚠️ Failed to recalculate position size for {symbol}: {e}")

                adjusted_positions.append(adjusted_pos)

        # Step 2: Apply buffer zones to position closures
        target_symbols = {pos['symbol'] for pos in target_positions}
        positions_to_close = self._apply_closure_buffer_zones(target_symbols, current_positions, buffer_config)

        # Step 3: Combine all positions that need trading
        all_positions_to_execute = adjusted_positions + positions_to_close

        # Step 4: Log comprehensive summary
        total_target_positions = len(target_positions)
        trade_positions = len(adjusted_positions)
        close_positions = len(positions_to_close)
        total_trades = len(all_positions_to_execute)

        logger.info(f"📊 Comprehensive buffer zone results:")
        logger.info(f"   Target positions: {total_target_positions}")
        logger.info(f"   Positions needing adjustments: {trade_positions}")
        logger.info(f"   Positions needing closure: {close_positions}")
        logger.info(f"   Positions in no-trade zone: {no_trade_count}")
        logger.info(f"   Total trades required: {total_trades}")
        if total_target_positions > 0:
            logger.info(f"   Trade reduction: {(no_trade_count/total_target_positions)*100:.1f}%")

        # Log individual actions for adjustments
        for pos in adjusted_positions:
            action_desc = {
                'trade_up': '↗️ Trade UP to lower bound',
                'trade_down': '↘️ Trade DOWN to upper bound'
            }.get(pos['buffer_action'], pos['buffer_action'])

            logger.info(f"   {pos['symbol']}: {action_desc} - "
                       f"${pos['current_usd']:,.0f} → ${pos['size_usd']:,.0f} "
                       f"(target: ${pos['original_target_usd']:,.0f})")

        # Log closure actions
        for pos in positions_to_close:
            logger.info(f"   {pos['symbol']}: 🗑️ CLOSE position - "
                       f"${pos['current_usd']:,.0f} → $0")

        return all_positions_to_execute

    def _calculate_portfolio_ev(self, target_positions: List[Dict]) -> float:
        """
        Calculate expected value (EV) for the entire portfolio.

        Formula:
        - ev_long_leg = sum of each coin in long leg's adjusted_funding * -1
        - ev_short_leg = sum of each coin in short leg's adjusted_funding
        - portfolio_ev = ev_long_leg + ev_short_leg

        Args:
            target_positions: List of target position dictionaries

        Returns:
            float: Portfolio expected value
        """
        if not target_positions:
            return 0.0

        ev_long_leg = 0.0
        ev_short_leg = 0.0

        for position in target_positions:
            # Fix: Use correct field name 'adjusted_funding' instead of 'funding_rate'
            adjusted_funding = float(position.get('adjusted_funding', 0))
            side = position.get('side', '')

            if side == 'long':
                # For longs: multiply by -1 (we get paid when funding is negative)
                ev_long_leg += adjusted_funding * -1
            elif side == 'short':
                # For shorts: use as-is (we get paid when funding is positive)
                ev_short_leg += adjusted_funding

        portfolio_ev = ev_long_leg + ev_short_leg

        logger.debug(f"📊 Portfolio EV breakdown:")
        logger.debug(f"   Long leg EV: {ev_long_leg:.6f} (from {sum(1 for p in target_positions if p.get('side') == 'long')} positions)")
        logger.debug(f"   Short leg EV: {ev_short_leg:.6f} (from {sum(1 for p in target_positions if p.get('side') == 'short')} positions)")
        logger.debug(f"   Total portfolio EV: {portfolio_ev:.6f}")

        return portfolio_ev

    def _get_trading_cost_threshold(self) -> float:
        """
        Get the trading cost threshold for the current exchange.

        Returns the negative of the trading cost adjustment, so portfolio EV
        must be greater than this threshold to be profitable.

        Returns:
            float: Trading cost threshold (negative value)
        """
        # Get exchange name
        exchange_name = getattr(self.exchange, 'name', 'unknown').lower()

        # Get trading cost adjustment from config
        trading_cost_config = self.config.get('trading_cost_adjustment', {})

        # Handle both old format (single value) and new format (per-exchange)
        if isinstance(trading_cost_config, dict):
            trading_cost = trading_cost_config.get(exchange_name, 0.0001)  # Default 0.01%
        else:
            # Fallback to old format
            trading_cost = trading_cost_config

        # Return negative of trading cost (threshold)
        threshold = -1 * float(trading_cost)

        logger.debug(f"📊 Trading cost threshold for {exchange_name}: {threshold:.6f} (cost: {trading_cost:.6f})")

        return threshold
