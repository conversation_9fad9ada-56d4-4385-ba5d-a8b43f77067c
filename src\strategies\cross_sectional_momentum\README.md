# Cross-Sectional Momentum Strategy

A momentum-based trading strategy that exploits price momentum patterns in cryptocurrency markets by taking long positions in assets with strong positive momentum and short positions in assets with strong negative momentum.

## Table of Contents

1. [Strategy Overview](#strategy-overview)
2. [Universe Selection](#universe-selection)
3. [Feature Engineering](#feature-engineering)
4. [Model](#model)
5. [Position Selection](#position-selection)
6. [Position Sizing](#position-sizing)
7. [Target Portfolio Generation](#target-portfolio-generation)
8. [Configuration Parameters](#configuration-parameters)
9. [Risk Management](#risk-management)
10. [Performance Characteristics](#performance-characteristics)

## Strategy Overview

The Cross-Sectional Momentum strategy captures momentum patterns in cryptocurrency markets by identifying assets with strong directional price movements. The strategy uses statistical measures of momentum to construct a market-neutral portfolio that benefits from momentum persistence.

**Core Principle**: Price momentum in cryptocurrency markets exhibits persistence over medium-term horizons (days to weeks). Assets with strong recent performance tend to continue outperforming in the near term.

## Universe Selection

### Selection Criteria
The strategy selects from the top cryptocurrency assets based on:

1. **Market Cap Ranking**: Top 50 coins by market capitalization
2. **Volume Filter**: Minimum daily volume of $5,000,000 USD
3. **Stablecoin Exclusion**: Remove stablecoins and USD-pegged assets
4. **Listing Age**: Exclude coins listed within the last 30 days
5. **Data Availability**: Minimum 60 days of historical price data

### Implementation
```python
async def get_universe(self) -> List[str]:
    # Fetch all available markets
    markets = await self.exchange.fetch_markets()
    usdt_symbols = [
        market['symbol'] for market in markets.values()
        if market.get('type') == 'swap' and market.get('quote') == 'USDT'
    ]
    
    # Exclude stablecoins
    stablecoin_keywords = ['USD', 'USDT', 'USDC', 'BUSD', 'DAI']
    filtered_symbols = [
        symbol for symbol in usdt_symbols
        if not any(keyword in symbol.upper() for keyword in stablecoin_keywords)
    ]
    
    # Sort by volume and take top N
    symbols_with_volume = []
    for symbol in filtered_symbols:
        ticker = await self.exchange.fetch_ticker(symbol)
        if ticker and ticker.get('quoteVolume', 0) >= min_volume:
            symbols_with_volume.append({
                'symbol': symbol,
                'volume_24h': ticker['quoteVolume']
            })
    
    symbols_with_volume.sort(key=lambda x: x['volume_24h'], reverse=True)
    return [item['symbol'] for item in symbols_with_volume[:50]]
```

### Universe Characteristics
- **Size**: Fixed at top 50 coins by market cap
- **Composition**: Major cryptocurrencies with high liquidity
- **Exclusions**: Stablecoins, new listings, low-volume assets
- **Updates**: Universe refreshed daily during rebalancing

## Feature Engineering

### Primary Feature: Momentum Z-Score

#### 1. Return Calculation
```python
# Calculate log returns for better statistical properties
returns = []
for i in range(1, len(closes)):
    if closes[i-1] > 0 and closes[i] > 0:
        returns.append(np.log(closes[i] / closes[i-1]))
```

#### 2. Momentum Calculation
```python
# Calculate 20-day cumulative momentum
lookback_days = 20
recent_returns = returns[-lookback_days:]
momentum = sum(recent_returns)
```

#### 3. Z-Score Normalization
```python
# Calculate rolling z-score over 60-day window
historical_momentums = []
for i in range(lookback_days, len(returns) - lookback_days + 1):
    period_returns = returns[i:i + lookback_days]
    historical_momentums.append(sum(period_returns))

mean_momentum = statistics.mean(historical_momentums)
std_momentum = statistics.stdev(historical_momentums)

momentum_zscore = (momentum - mean_momentum) / std_momentum
```

### Supporting Features

#### 1. Volatility Metrics
Weighted volatility for risk management:
```python
weighted_volatility = (
    0.3 * volatility_60d +
    0.5 * volatility_30d +
    0.2 * volatility_10d
)
```

#### 2. Beta Calculation
60-day rolling beta against BTC:
```python
beta_60d = calculate_rolling_beta(returns, btc_returns, window=60)
```

### Feature Validation
- **Outlier Handling**: Cap extreme z-scores at ±5.0
- **Data Quality**: Validate price data completeness
- **Statistical Validity**: Ensure sufficient observations for z-score calculation

## Model

### Momentum Persistence Framework
The strategy assumes momentum persistence over the holding period:

```python
Expected_Return = momentum_zscore * persistence_factor - transaction_costs
```

**Key Assumptions**:
- Momentum signals persist for 1-5 days on average
- Cross-sectional ranking provides relative advantage
- Transaction costs are predictable and manageable

### Sigmoid Weighting Function
Position weights based on momentum strength:

```python
def momentum_sigmoid_function(momentum_zscore, peak_momentum):
    abs_momentum = abs(momentum_zscore)
    ratio = abs_momentum / peak_momentum
    y = abs_momentum * exp(-(ratio ** 2))
    return y / (peak_momentum * exp(-1))  # Normalize to [0,1]
```

**Parameters**:
- **Peak Momentum Z-Score**: 2.0 (point of maximum weight)
- **Shape**: Bell curve optimized for momentum signals
- **Range**: [0, 1] normalized weights

## Position Selection

### Selection Process

#### 1. Momentum Classification
```python
long_threshold = 0.5   # Minimum z-score for long positions
short_threshold = -0.5 # Maximum z-score for short positions

for symbol_data in enriched_symbols:
    momentum_zscore = symbol_data['momentum_zscore_20d']
    
    # Calculate sigmoid weight
    momentum_weight = momentum_sigmoid_function(momentum_zscore, peak_momentum)
    symbol_data['momentum_weight'] = momentum_weight
    
    # Classify position direction
    if momentum_zscore >= long_threshold:
        potential_longs.append(symbol_data)
    elif momentum_zscore <= short_threshold:
        potential_shorts.append(symbol_data)
```

#### 2. Three-Tier Ranking System
Positions ranked by:
1. **Primary**: Momentum weight (sigmoid-weighted z-score)
2. **Secondary**: Absolute momentum z-score
3. **Tertiary**: 24-hour volume (tie-breaker)

#### 3. Cross-Sectional Selection
- **Relative Ranking**: Focus on relative momentum strength
- **Market Neutrality**: Balance long and short exposure
- **Diversification**: Minimum 3 positions per leg

### Selection Constraints
- **Minimum Positions**: At least 3 positions per leg
- **Momentum Threshold**: Minimum absolute z-score of 0.5
- **Maximum Exposure**: 80% of capital to momentum positions

## Position Sizing

### Volatility Targeting
Target portfolio volatility through position sizing:

```python
target_volatility = 0.25  # 25% annualized for momentum strategy

for position in candidates:
    leverage = target_volatility / position['weighted_volatility']
    position['leverage'] = min(max(leverage, min_leverage), max_leverage)
    position['weight'] = position['momentum_weight'] * position['leverage']
```

### Beta Neutrality
Adjust weights for market neutrality:

```python
# Calculate portfolio beta
portfolio_beta = sum(weight * beta for weight, beta in zip(weights, betas))

# Optimize for beta neutrality if enabled
if enable_beta_projection and abs(portfolio_beta) > tolerance:
    weights = optimize_beta_neutral_weights(weights, betas)
```

### Position Constraints
- **Maximum Position Size**: 20% of total capital per position
- **Leverage Limits**: 0.1x to 3.0x leverage (conservative for momentum)
- **Minimum Position Size**: $25 USD equivalent

## Target Portfolio Generation

### Portfolio Construction Process

#### 1. Weight Calculation
```python
# Calculate momentum-based weights
for candidate in all_candidates:
    momentum_weight = candidate['momentum_weight']
    volatility_adjustment = target_vol / candidate['volatility']
    candidate['final_weight'] = momentum_weight * volatility_adjustment
```

#### 2. Normalization and Allocation
```python
# Normalize weights and allocate capital
total_weight = sum(pos['final_weight'] for pos in all_positions)
total_capital = config['total_capital_usd']

for position in all_positions:
    position['normalized_weight'] = position['final_weight'] / total_weight
    position['size_usd'] = position['normalized_weight'] * total_capital
    position['size_native'] = position['size_usd'] / position['price']
```

#### 3. StrategyPosition Creation
```python
strategy_positions = []
for pos in target_positions:
    strategy_position = StrategyPosition(
        symbol=pos['symbol'],
        side=pos['side'],
        size_usd=pos['size_usd'],
        size_native=pos['size_native'],
        weight=pos['weight'],
        confidence=1.0,
        metadata={
            'momentum_zscore': pos['momentum_zscore'],
            'momentum_weight': pos['momentum_weight'],
            'volatility': pos['volatility'],
            'strategy_source': 'cross_sectional_momentum'
        }
    )
    strategy_positions.append(strategy_position)
```

### Portfolio Characteristics
- **Market Neutrality**: Balanced long/short momentum exposure
- **Concentration**: Focused on strongest momentum signals
- **Risk Control**: Volatility-targeted with momentum-specific limits
- **Turnover**: Moderate turnover based on momentum persistence

## Configuration Parameters

### Key Parameters
```yaml
# Universe Selection
top_coins_by_market_cap: 50
min_daily_volume_usd: 5000000
exclude_stablecoins: true

# Feature Calculation
momentum_lookback_days: 20
zscore_window_days: 60
peak_momentum_zscore: 2.0

# Position Selection
long_momentum_threshold: 0.5
short_momentum_threshold: -0.5
min_positions_per_leg: 3

# Position Sizing
max_position_capital_pct: 20
target_volatility: 0.25
max_leverage: 3.0

# Risk Management
max_momentum_exposure: 0.8
buffer_zone_tolerance_percentage: 7.5
momentum_exit_threshold: 0.1
```

## Risk Management

### Momentum-Specific Controls
- **Momentum Decay**: Monitor signal degradation over time
- **Reversal Protection**: Exit positions when momentum reverses
- **Exposure Limits**: Maximum 80% capital to momentum positions
- **Concentration Limits**: Maximum 20% per position

### Portfolio-Level Controls
- **Volatility Targeting**: Maintain 25% target volatility
- **Beta Neutrality**: Optional market-neutral construction
- **Drawdown Limits**: Monitor maximum drawdown levels
- **Correlation Monitoring**: Track position correlations

### Position-Level Controls
- **Buffer Zones**: 7.5% tolerance around target positions
- **Exit Thresholds**: Close positions when momentum falls below 0.1
- **Leverage Limits**: Conservative 3.0x maximum leverage
- **Minimum Thresholds**: $25 minimum position size

## Performance Characteristics

### Expected Returns
- **Source**: Momentum persistence and cross-sectional ranking
- **Frequency**: Daily rebalancing based on momentum signals
- **Volatility**: Targeted at 25% annualized
- **Sharpe Ratio**: Target >1.2 through momentum capture

### Risk Profile
- **Momentum Risk**: Primary risk from momentum reversals
- **Market Risk**: Minimized through beta neutrality
- **Concentration Risk**: Managed through position limits
- **Liquidity Risk**: Low due to top 50 coin universe

### Performance Metrics
- **Hit Rate**: Percentage of profitable momentum signals
- **Average Hold Period**: Typical momentum persistence duration
- **Turnover**: Portfolio turnover rate from rebalancing
- **Risk-Adjusted Returns**: Sharpe ratio and information ratio

### Strategy Characteristics
- **Market Regime Sensitivity**: Performs best in trending markets
- **Correlation**: Low correlation with funding arbitrage strategies
- **Capacity**: Scalable within top 50 coin universe
- **Implementation**: Suitable for medium-frequency trading
