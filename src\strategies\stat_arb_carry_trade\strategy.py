"""
Statistical Arbitrage Carry Trade Strategy Implementation (Simplified)

This strategy implements funding arbitrage by:
1. Selecting universe based on volume and volatility filters (no funding rate bounds)
2. Calculating annualized funding rates with fixed 10.95% trading cost adjustment
3. Using simple top 5 selection (most negative for longs, most positive for shorts)
4. Applying equal dollar weighting + volatility targeting + beta neutrality
"""

import logging
from typing import Dict, List, Optional, Any, Tuple
import yaml
import os
from pathlib import Path

from strategies.base import BaseStrategy, StrategyPosition
from utils.data_validation import DataUnitValidator

logger = logging.getLogger(__name__)


class StatArbCarryTradeStrategy(BaseStrategy):
    """
    Statistical Arbitrage Carry Trade Strategy (Simplified)

    Implements funding arbitrage using simplified top 5 selection and
    equal dollar weighting with volatility targeting and optional beta neutrality.
    """
    
    def __init__(self, data_fetcher, data_analyzer, exchange, main_config: Dict[str, Any]):
        """
        Initialize the strategy with shared resources
        
        Args:
            data_fetcher: Shared data fetcher instance
            data_analyzer: Shared data analyzer instance
            exchange: Shared exchange interface
            main_config: Main configuration dictionary
        """
        # Load strategy-specific configuration
        strategy_config = self._load_strategy_config()
        
        # Initialize base strategy
        super().__init__(
            strategy_name="stat_arb_carry_trade",
            config=strategy_config,
            data_fetcher=data_fetcher,
            data_analyzer=data_analyzer,
            exchange=exchange
        )
        
        # Store main config for shared parameters
        self.main_config = main_config
        
        # Extract frequently used parameters
        self.total_capital = main_config.get('total_capital_usd', 10000)
        self.exchange_name = main_config.get('exchange', 'bybit')
        
        self.logger.info(f"🏗️ StatArb Carry Trade strategy initialized")
        self.logger.info(f"   Total capital: ${self.total_capital:,.0f}")
        self.logger.info(f"   Exchange: {self.exchange_name}")
    
    def _load_strategy_config(self) -> Dict[str, Any]:
        """Load strategy-specific configuration from YAML file"""
        try:
            config_path = Path(__file__).parent / "config.yaml"
            
            if not config_path.exists():
                raise FileNotFoundError(f"Strategy config file not found: {config_path}")
            
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            
            logger.info(f"📂 Loaded strategy config from {config_path}")
            return config
            
        except Exception as e:
            logger.error(f"❌ Failed to load strategy config: {e}")
            raise
    
    def _validate_config(self) -> None:
        """Validate strategy-specific configuration parameters"""
        required_params = [
            'min_daily_volume_usd',
            'peak_abs_funding_rate', 
            'max_position_capital_pct',
            'target_volatility',
            'min_positions_per_leg'
        ]
        
        for param in required_params:
            if param not in self.config:
                raise ValueError(f"Missing required strategy parameter: {param}")
        
        # Validate parameter ranges
        if self.config['peak_abs_funding_rate'] <= 0:
            raise ValueError("peak_abs_funding_rate must be positive")
        
        if not 0 < self.config['max_position_capital_pct'] <= 100:
            raise ValueError("max_position_capital_pct must be between 0 and 100")
        
        if self.config['target_volatility'] <= 0:
            raise ValueError("target_volatility must be positive")
        
        if self.config['min_positions_per_leg'] < 1:
            raise ValueError("min_positions_per_leg must be at least 1")
        
        self.logger.info("✅ Strategy configuration validated")
    
    async def get_universe(self) -> List[str]:
        """
        Get the universe of symbols for funding arbitrage
        
        Applies volume, listing age, and data availability filters.
        
        Returns:
            List of eligible symbol strings
        """
        try:
            self.logger.info("🌍 Fetching universe for funding arbitrage strategy...")
            
            # Get eligible coins using the shared data fetcher
            eligible_coins = await self.data_fetcher.get_eligible_coins()
            
            if not eligible_coins:
                self.logger.warning("⚠️ No eligible coins found from data fetcher")
                return []
            
            # Extract symbols from eligible coins
            universe = [coin['symbol'] for coin in eligible_coins]
            
            self.logger.info(f"✅ Strategy universe contains {len(universe)} symbols")
            
            # Log sample of universe for debugging
            if self.config.get('log_position_selection_details', False):
                sample_size = min(10, len(universe))
                sample_symbols = universe[:sample_size]
                self.logger.debug(f"📊 Universe sample: {sample_symbols}")
            
            return universe
            
        except Exception as e:
            self.logger.error(f"❌ Failed to get strategy universe: {e}")
            raise
    
    async def calculate_features(self, symbols: List[str]) -> List[Dict[str, Any]]:
        """
        Calculate funding arbitrage features for symbols
        
        Features include:
        - Funding rates and cost adjustments
        - Volatility metrics
        - Beta values (if enabled)
        - Volume metrics
        
        Args:
            symbols: List of symbols to calculate features for
            
        Returns:
            List of enriched symbol dictionaries with calculated features
        """
        try:
            self.logger.info(f"📊 Calculating features for {len(symbols)} symbols...")
            
            # Get eligible coins data (includes funding rates, volume, etc.)
            eligible_coins = await self.data_fetcher.get_eligible_coins()
            
            # Filter to only requested symbols
            symbol_set = set(symbols)
            filtered_coins = [
                coin for coin in eligible_coins 
                if coin['symbol'] in symbol_set
            ]
            
            if not filtered_coins:
                self.logger.warning("⚠️ No coins found matching requested symbols")
                return []
            
            self.logger.info(f"📊 Found data for {len(filtered_coins)} symbols")
            
            # Enrich with volatility and beta data using shared analyzer
            enriched_coins = await self.data_analyzer.enrich_coins_with_volatility(filtered_coins)
            
            if not enriched_coins:
                self.logger.warning("⚠️ No symbols passed volatility enrichment")
                return []
            
            self.logger.info(f"✅ Successfully calculated features for {len(enriched_coins)} symbols")
            
            # Log feature calculation details if enabled
            if self.config.get('log_ev_calculations', False):
                for coin in enriched_coins[:5]:  # Log first 5 for debugging
                    self.logger.debug(f"📊 {coin['symbol']}: funding={coin.get('adjusted_funding', 'N/A'):.6f}, "
                                    f"vol={coin.get('weighted_volatility', 'N/A'):.4f}")
            
            return enriched_coins
            
        except Exception as e:
            self.logger.error(f"❌ Failed to calculate features: {e}")
            raise
    
    async def select_positions(self, enriched_symbols: List[Dict[str, Any]]) -> Tuple[List[Dict], List[Dict]]:
        """
        Select long and short position candidates using EV-based approach
        
        Uses the shared data analyzer's EV-based position selection logic
        with sigmoid weighting and three-tier ranking.
        
        Args:
            enriched_symbols: Symbols with calculated features
            
        Returns:
            Tuple of (long_candidates, short_candidates)
        """
        try:
            self.logger.info(f"🎯 Selecting positions from {len(enriched_symbols)} enriched symbols...")
            
            # Get current positions for cost adjustment logic
            try:
                current_positions_list = await self.exchange.fetch_positions()
                current_positions = {}
                for pos in current_positions_list:
                    if pos.get('contracts', 0) != 0:  # Only non-zero positions
                        symbol = pos['symbol']
                        current_positions[symbol] = pos
                self.logger.info(f"📊 Found {len(current_positions)} currently held positions")
            except Exception as e:
                self.logger.warning(f"⚠️ Failed to fetch current positions: {e}")
                current_positions = {}
            
            # Use shared analyzer for position selection
            long_candidates, short_candidates = self.data_analyzer.select_positions_ev_based(
                enriched_symbols, current_positions
            )
            
            self.logger.info(f"✅ Selected {len(long_candidates)} long and {len(short_candidates)} short candidates")
            
            # Log selection details if enabled
            if self.config.get('log_position_selection_details', False):
                if long_candidates:
                    self.logger.info(f"📊 Top 3 long candidates:")
                    for i, candidate in enumerate(long_candidates[:3]):
                        self.logger.info(f"   {i+1}. {candidate['symbol']}: "
                                       f"funding={candidate.get('adjusted_funding', 'N/A'):.6f}, "
                                       f"weight={candidate.get('ev_weight', 'N/A'):.4f}")
                
                if short_candidates:
                    self.logger.info(f"📊 Top 3 short candidates:")
                    for i, candidate in enumerate(short_candidates[:3]):
                        self.logger.info(f"   {i+1}. {candidate['symbol']}: "
                                       f"funding={candidate.get('adjusted_funding', 'N/A'):.6f}, "
                                       f"weight={candidate.get('ev_weight', 'N/A'):.4f}")
            
            return long_candidates, short_candidates
            
        except Exception as e:
            self.logger.error(f"❌ Failed to select positions: {e}")
            raise

    async def size_positions(self, long_candidates: List[Dict],
                           short_candidates: List[Dict]) -> List[StrategyPosition]:
        """
        Calculate position sizes using EV-based weighting and volatility targeting

        Uses the shared position manager's EV-based sizing logic with:
        - Sigmoid weighting based on adjusted funding rates
        - Volatility targeting for risk management
        - Beta neutrality (if enabled)
        - Maximum position size constraints

        Args:
            long_candidates: Selected long position candidates
            short_candidates: Selected short position candidates

        Returns:
            List of StrategyPosition objects with calculated sizes
        """
        try:
            self.logger.info(f"💰 Calculating position sizes for {len(long_candidates)} longs and {len(short_candidates)} shorts...")

            # Create a temporary position manager with strategy-specific config
            # We need to merge main config with strategy config for position manager
            merged_config = self.main_config.copy()
            merged_config.update(self.config)

            # Import here to avoid circular imports
            from execution.position_manager import PositionManager

            position_manager = PositionManager(self.exchange, self.data_analyzer, merged_config)

            # Calculate position sizes using EV-based approach
            target_positions = await position_manager.calculate_position_sizes_ev_based(
                long_candidates, short_candidates
            )

            if not target_positions:
                self.logger.warning("⚠️ No viable positions calculated")
                return []

            # Convert to StrategyPosition objects
            strategy_positions = []
            for pos in target_positions:
                strategy_position = StrategyPosition(
                    symbol=pos['symbol'],
                    side=pos['side'],
                    size_usd=pos['size_usd'],
                    size_native=pos['size_native'],
                    weight=pos.get('weight', 0.0),
                    confidence=1.0,  # Default confidence
                    metadata={
                        'adjusted_funding': pos.get('adjusted_funding', 0.0),
                        'volatility': pos.get('volatility', 0.0),
                        'leverage': pos.get('leverage', 1.0),
                        'strategy_source': 'stat_arb_carry_trade'
                    }
                )
                strategy_positions.append(strategy_position)

            total_capital = sum(pos.size_usd for pos in strategy_positions)
            long_count = len([pos for pos in strategy_positions if pos.side == 'long'])
            short_count = len([pos for pos in strategy_positions if pos.side == 'short'])

            self.logger.info(f"✅ Calculated sizes for {len(strategy_positions)} positions:")
            self.logger.info(f"   Long: {long_count} positions")
            self.logger.info(f"   Short: {short_count} positions")
            self.logger.info(f"   Total capital: ${total_capital:,.0f}")

            return strategy_positions

        except Exception as e:
            self.logger.error(f"❌ Failed to calculate position sizes: {e}")
            raise

    def get_strategy_info(self) -> Dict[str, Any]:
        """Get detailed information about this strategy"""
        base_info = super().get_strategy_info()

        strategy_info = {
            **base_info,
            'description': 'Statistical Arbitrage Carry Trade Strategy (Simplified)',
            'strategy_type': 'funding_arbitrage',
            'universe_selection': 'volume_and_volatility_filtered_no_funding_bounds',
            'position_selection': 'top_5_by_adjusted_annualized_funding_rate',
            'position_sizing': 'equal_dollar_weights_with_volatility_targeting_and_beta_neutrality',
            'risk_management': 'portfolio_ev_threshold_and_buffer_zones',
            'exchange': self.exchange_name,
            'total_capital': self.total_capital,
            'key_parameters': {
                'max_positions_per_leg': self.config.get('max_positions_per_leg', 5),
                'target_volatility': self.config.get('target_volatility'),
                'max_position_capital_pct': self.config.get('max_position_capital_pct'),
                'trading_cost_adjustment_annualized': self.config.get('trading_cost_adjustment_annualized', 0.1095),
                'min_daily_volume_usd': self.config.get('min_daily_volume_usd')
            }
        }

        return strategy_info

    def validate_strategy_health(self) -> Dict[str, Any]:
        """
        Validate strategy health and configuration

        Returns:
            Dictionary containing health check results
        """
        health_check = {
            'overall_health': 'healthy',
            'issues': [],
            'warnings': [],
            'config_validation': True,
            'data_access': True,
            'exchange_access': True
        }

        try:
            # Validate configuration
            self._validate_config()
        except Exception as e:
            health_check['config_validation'] = False
            health_check['issues'].append(f"Config validation failed: {e}")
            health_check['overall_health'] = 'unhealthy'

        # Check data fetcher access
        if not self.data_fetcher:
            health_check['data_access'] = False
            health_check['issues'].append("Data fetcher not available")
            health_check['overall_health'] = 'unhealthy'

        # Check exchange access
        if not self.exchange:
            health_check['exchange_access'] = False
            health_check['issues'].append("Exchange interface not available")
            health_check['overall_health'] = 'unhealthy'

        # Check for potential warnings
        if self.config.get('max_position_capital_pct', 0) > 50:
            health_check['warnings'].append("High max position capital percentage (>50%)")

        if self.config.get('min_positions_per_leg', 0) < 2:
            health_check['warnings'].append("Low minimum positions per leg (<2) may reduce diversification")

        # Set overall health based on issues
        if health_check['issues']:
            health_check['overall_health'] = 'unhealthy'
        elif health_check['warnings']:
            health_check['overall_health'] = 'warning'

        return health_check
