# Statistical Arbitrage Carry Trade Strategy Configuration
# Strategy-specific parameters for funding arbitrage

# ============================================================================
# UNIVERSE SELECTION PARAMETERS
# ============================================================================

# Volume and liquidity filters
min_daily_volume_usd: 1000000      # Minimum 5-day mean volume filter ($1M)
exclude_new_listings_days: 60      # Exclude coins listed within this many days
min_historical_data_days: 60       # Minimum days of historical data required

# Volatility filters
min_volatility_threshold: 0.05     # Minimum volatility to exclude stablecoins (5%)

# ============================================================================
# FEATURE CALCULATION PARAMETERS
# ============================================================================

# Funding rate analysis
funding_rate_lookback_days: 3      # Days for funding rate averaging
trading_cost_adjustment_annualized: 0.1095  # 10.95% annualized trading cost

# Volatility calculation weights
volatility_weights:
  vol_60d: 0.3                     # 30% weight for 60-day volatility
  vol_30d: 0.5                     # 50% weight for 30-day volatility
  vol_10d: 0.2                     # 20% weight for 10-day volatility

# Beta calculation (if enabled)
beta_calculation_days: 60          # Number of days for rolling beta calculation
market_index_symbol: "BTCUSDT"     # Market index symbol for beta calculation

# ============================================================================
# POSITION SELECTION PARAMETERS
# ============================================================================

# Top 5 selection approach
max_positions_per_leg: 5           # Fixed 5 positions per leg

# Position ranking criteria (triple-tier system)
# 1. Primary: Adjusted annualized funding rate (most negative for longs, most positive for shorts)
# 2. Secondary: Raw annualized funding rate (tie-breaker for equal adjusted rates)
# 3. Tertiary: 5-day average volume (final tie-breaker, highest volume first)

# ============================================================================
# POSITION SIZING PARAMETERS
# ============================================================================

# Capital allocation
# Start with equal dollar weights (1/N), then apply volatility targeting and beta projection
max_position_capital_pct: 25       # Maximum capital per position (25%)
target_volatility: 0.30            # Target portfolio volatility (30%)

# Risk management
enable_beta_projection: false       # Disable beta-neutral portfolio construction for this strategy
beta_neutrality_tolerance: 0.05    # Maximum allowed portfolio beta deviation (±5%)
beta_optimization_max_weight_change: 0.20  # Maximum weight change during beta optimization (20%)



# ============================================================================
# STRATEGY-SPECIFIC RISK CONTROLS
# ============================================================================

# Portfolio-level controls
portfolio_ev_threshold_multiplier: -1.0  # Close all positions when portfolio EV < trading_cost * this multiplier

# Buffer zones for position management
buffer_zone_tolerance_percentage: 5.0    # Buffer zone around target position (% of individual coin's USD position)

# Position closure thresholds
min_close_threshold_usd: 10        # Minimum USD value to trigger position closure

# ============================================================================
# EXECUTION PREFERENCES
# ============================================================================

# Order execution preferences (strategy-specific overrides)
preferred_execution_style: "conservative"  # conservative, balanced, aggressive
max_execution_time_minutes: 30     # Maximum time to spend executing this strategy's positions

# Position update frequency
position_update_frequency: "daily" # daily, twice_daily, hourly

# ============================================================================
# PERFORMANCE AND MONITORING
# ============================================================================

# Strategy-specific monitoring
enable_funding_rate_alerts: true   # Alert on extreme funding rate changes
funding_rate_alert_threshold: 0.005  # Alert threshold for funding rate changes (0.5%)

# Performance tracking
track_individual_position_pnl: true  # Track P&L for each position
enable_strategy_attribution: true   # Enable detailed strategy attribution

# Logging preferences
log_position_selection_details: true  # Log detailed position selection reasoning
log_ev_calculations: true           # Log EV calculation details

# ============================================================================
# FALLBACK AND DEFAULT VALUES
# ============================================================================

# Default values for missing data
default_volatility: 0.20           # Default volatility fallback (20%)
default_beta: 1.0                   # Default beta fallback (1.0 = market beta)

# Error handling
skip_problematic_symbols: true     # Skip symbols that fail validation
max_symbol_failures_per_execution: 5  # Maximum symbol failures before stopping

# Data validation
validate_funding_rates: true       # Validate funding rate data quality
validate_volume_data: true         # Validate volume data quality
validate_price_data: true          # Validate price data quality
