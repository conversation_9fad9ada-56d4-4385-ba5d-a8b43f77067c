# Multi-Strategy Trading System

A sophisticated multi-strategy cryptocurrency trading system that executes multiple trading strategies in parallel, intelligently combines their portfolios, and provides comprehensive performance attribution.

## Table of Contents

1. [System Architecture](#system-architecture)
2. [End-to-End Flow](#end-to-end-flow)
3. [Quick Start](#quick-start)
4. [User Guide](#user-guide)
   - [Configuration Management](#configuration-management)
   - [Adding New Strategies](#adding-new-strategies)
   - [Strategy Weights and Combination](#strategy-weights-and-combination)
   - [Performance Monitoring](#performance-monitoring)
5. [Technical Guide](#technical-guide)
   - [System Initialization](#system-initialization)
   - [Exchange API Integration](#exchange-api-integration)
   - [Data Fetching and Caching](#data-fetching-and-caching)
   - [Portfolio Generation and Combination](#portfolio-generation-and-combination)
   - [Execution Engine](#execution-engine)
   - [Maintenance and Monitoring](#maintenance-and-monitoring)
6. [Available Strategies](#available-strategies)
7. [Features](#features)
8. [Requirements](#requirements)
9. [License](#license)

## System Architecture

The system follows a modular multi-strategy architecture with the following core components:

```
┌─────────────────────────────────────────────────────────────────┐
│                    Multi-Strategy Orchestrator                  │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   Strategy 1    │  │   Strategy 2    │  │   Strategy N    │  │
│  │   (StatArb)     │  │  (Momentum)     │  │   (Future)      │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
├─────────────────────────────────────────────────────────────────┤
│                    Portfolio Combiner                          │
│              (Position Netting & Weight Application)            │
├─────────────────────────────────────────────────────────────────┤
│                    Execution Engine                            │
│              (Randomized Order Placement)                      │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │  Data Fetcher   │  │  Data Analyzer  │  │ Performance     │  │
│  │   & Cache       │  │                 │  │   Tracker       │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
├─────────────────────────────────────────────────────────────────┤
│                    Exchange Interface                          │
│           (Bybit, Binance, OKX, Hyperliquid)                   │
└─────────────────────────────────────────────────────────────────┘
```

### Core Components

- **Multi-Strategy Orchestrator**: Coordinates all system components and manages the execution lifecycle
- **Strategy Framework**: Modular strategy implementation with shared resources
- **Portfolio Combiner**: Intelligently combines strategy portfolios with position netting
- **Execution Engine**: Randomized order placement with post-only preference
- **Data Layer**: Efficient data fetching, caching, and analysis
- **Performance Tracker**: Comprehensive performance attribution and monitoring

## End-to-End Flow

The system operates in a continuous cycle with the following flow:

```
1. System Initialization
   ├── Load configuration and validate parameters
   ├── Initialize exchange connections and credentials
   ├── Register and validate all enabled strategies
   └── Setup data cache and performance tracking

2. Data Fetching Phase
   ├── Fetch market data (OHLCV, funding rates, volumes)
   ├── Cache data for shared access across strategies
   └── Validate data quality and completeness

3. Strategy Execution Phase (Parallel)
   ├── Strategy 1: Universe → Features → Selection → Sizing
   ├── Strategy 2: Universe → Features → Selection → Sizing
   └── Strategy N: Universe → Features → Selection → Sizing

4. Portfolio Combination Phase
   ├── Apply strategy weights to individual positions
   ├── Net long/short positions by symbol
   ├── Filter positions below minimum thresholds
   └── Generate final combined portfolio

5. Execution Phase
   ├── Calculate position deltas vs current holdings
   ├── Generate randomized order batches
   ├── Execute trades with post-only preference
   └── Monitor order fills and retry logic

6. Performance Tracking
   ├── Record individual strategy performance
   ├── Track combined portfolio metrics
   ├── Update performance attribution
   └── Export data for analysis

7. Monitoring & Maintenance
   ├── Monitor active positions
   ├── Check for rebalancing triggers
   ├── Handle errors and recovery
   └── Schedule next execution cycle
```

## Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Configure System
Copy and edit the configuration file:
```bash
cp config.example.yaml config.yaml
```

Edit `config.yaml` with your settings:
```yaml
# Strategy Configuration
strategies:
  stat_arb_carry_trade:
    enabled: true
    weight: 1.0

# Exchange Configuration
exchange: bybit
use_testnet: true
total_capital_usd: 10000
simulation_mode: true
```

### 3. Setup Credentials
Run the system to setup exchange credentials interactively:
```bash
python main.py
```

### 4. Start Trading
The system will automatically:
- Initialize all enabled strategies
- Fetch and cache market data
- Execute strategies in parallel
- Combine portfolios and execute trades
- Monitor and rebalance positions

## User Guide

### Configuration Management

The system uses a hierarchical configuration structure:

- **Main Configuration** (`config.yaml`): Shared parameters, strategy weights, exchange settings
- **Strategy Configurations** (`src/strategies/*/config.yaml`): Strategy-specific parameters

#### Main Configuration Structure
```yaml
# Strategy weights and enablement
strategies:
  strategy_name:
    enabled: true/false
    weight: float  # Portfolio allocation weight

# Shared parameters
total_capital_usd: 10000
exchange: "bybit"
simulation_mode: true

# Portfolio combination settings
portfolio_combination:
  enable_position_netting: true
  min_position_size_usd: 10.0

# Performance tracking
performance_tracking:
  enable_detailed_performance_tracking: true
  max_performance_history_days: 90
```

### Adding New Strategies

To add a new trading strategy:

#### 1. Create Strategy Structure
```bash
mkdir src/strategies/my_new_strategy
touch src/strategies/my_new_strategy/__init__.py
touch src/strategies/my_new_strategy/strategy.py
touch src/strategies/my_new_strategy/config.yaml
```

#### 2. Implement Strategy Class
Create `strategy.py` inheriting from `BaseStrategy`:
```python
from strategies.base import BaseStrategy, StrategyPosition

class MyNewStrategy(BaseStrategy):
    async def get_universe(self) -> List[str]:
        # Return list of symbols to trade
        pass

    async def calculate_features(self, symbols: List[str]) -> List[Dict]:
        # Calculate strategy-specific features
        pass

    async def select_positions(self, enriched_symbols: List[Dict]) -> Tuple[List, List]:
        # Return (long_candidates, short_candidates)
        pass

    async def size_positions(self, long_candidates: List, short_candidates: List) -> List[StrategyPosition]:
        # Return list of StrategyPosition objects
        pass
```

#### 3. Create Strategy Configuration
Create `config.yaml` with strategy-specific parameters:
```yaml
# Universe selection parameters
min_daily_volume_usd: 1000000
exclude_new_listings_days: 60

# Feature calculation parameters
feature_lookback_days: 20
feature_threshold: 0.5

# Position selection parameters
max_positions_per_leg: 10
min_positions_per_leg: 2

# Position sizing parameters
max_position_capital_pct: 20
target_volatility: 0.25
```

#### 4. Register Strategy
Add to `src/core/orchestrator.py` in `_initialize_strategies()`:
```python
elif strategy_name == 'my_new_strategy':
    from strategies.my_new_strategy import MyNewStrategy
    strategy = MyNewStrategy(
        data_fetcher=self.data_fetcher,
        data_analyzer=self.data_analyzer,
        exchange=self.exchange,
        main_config=self.config.to_dict()
    )
    self.strategy_manager.register_strategy(strategy, weight)
```

#### 5. Enable in Main Config
Add to `config.yaml`:
```yaml
strategies:
  my_new_strategy:
    enabled: true
    weight: 0.3
```

### Strategy Weights and Combination

Strategy weights determine portfolio allocation:

- **Weight Normalization**: Weights are automatically normalized to sum to 1.0
- **Position Netting**: Long/short positions in the same symbol are netted
- **Attribution Tracking**: Each position tracks contributing strategies
- **Minimum Thresholds**: Positions below minimum size are filtered out

Example with multiple strategies:
```yaml
strategies:
  stat_arb_carry_trade:
    enabled: true
    weight: 0.6  # 60% allocation
  momentum_strategy:
    enabled: true
    weight: 0.4  # 40% allocation
```

### Performance Monitoring

The system provides comprehensive performance tracking:

#### Strategy-Level Metrics
- Individual execution times and success rates
- Position counts and capital allocation
- Strategy-specific performance attribution
- Historical performance data

#### Portfolio-Level Metrics
- Combined portfolio performance
- Strategy contribution analysis
- Risk metrics and statistics
- Export capability for external analysis

#### Accessing Performance Data
Performance data is automatically saved to `performance_history.json` and can be exported:
```python
# Performance data is accessible through the PerformanceTracker
tracker.get_strategy_performance_summary('strategy_name', days=30)
tracker.get_portfolio_performance_summary(days=30)
tracker.export_performance_data('export_file.json')
```

## Technical Guide

### System Initialization

The system initialization follows a structured sequence:

#### 1. Configuration Loading
```python
# Load main configuration
config = load_config('config.yaml')

# Validate configuration parameters
ConfigValidator.validate(config.to_dict())

# Setup logging with configured parameters
setup_logging(debug_mode=config.get('debug_mode', False))
```

#### 2. Exchange Connection Setup
```python
# Create exchange instance based on configuration
exchange_name = config.get('exchange', 'bybit')
exchange = ExchangeFactory.create_exchange(exchange_name)

# Initialize with credentials and settings
exchange_config = config.get_exchange_config(exchange_name)
await exchange.initialize(exchange_config)
```

#### 3. Component Initialization
```python
# Initialize shared data components
data_cache = DataCache()
data_fetcher = DataFetcher(exchange, config, data_cache, performance_monitor)
data_analyzer = DataAnalyzer(data_fetcher, config)

# Initialize execution and portfolio components
executor = RandomizedExecutor(exchange, config)
portfolio_combiner = PortfolioCombiner(config)
performance_tracker = PerformanceTracker(config)

# Initialize strategy management
strategy_manager = StrategyManager(config)
```

#### 4. Strategy Registration
```python
# Register each enabled strategy
for strategy_name, strategy_config in strategies_config.items():
    if strategy_config.get('enabled', True):
        strategy = create_strategy(strategy_name, shared_resources)
        weight = strategy_config.get('weight', 1.0)
        strategy_manager.register_strategy(strategy, weight)
```

### Exchange API Integration

The system supports multiple exchanges through a unified interface:

#### Supported Exchanges
- **Bybit**: Perpetual futures with funding rates
- **Binance**: Spot and futures markets
- **OKX**: Comprehensive derivatives support
- **Hyperliquid**: Decentralized perpetual futures

#### Exchange Interface
```python
class ExchangeInterface:
    async def fetch_markets(self) -> Dict
    async def fetch_ticker(self, symbol: str) -> Dict
    async def fetch_ohlcv(self, symbol: str, timeframe: str, limit: int) -> List
    async def fetch_funding_rate(self, symbol: str) -> Dict
    async def fetch_positions(self) -> List
    async def create_order(self, symbol: str, type: str, side: str, amount: float) -> Dict
```

#### Connection Management
- **Credential Security**: Encrypted storage with interactive setup
- **Rate Limiting**: Automatic rate limit handling and backoff
- **Error Recovery**: Robust error handling with retry logic
- **Connection Pooling**: Efficient connection reuse

### Data Fetching and Caching

The data layer provides efficient, shared access to market data:

#### Data Cache Architecture
```python
class DataCache:
    def __init__(self):
        self.ohlcv_cache = {}      # OHLCV data by symbol/timeframe
        self.funding_cache = {}    # Funding rate data
        self.ticker_cache = {}     # Real-time ticker data
        self.market_cache = {}     # Market information
```

#### Caching Strategy
- **Time-based Expiration**: Data expires based on freshness requirements
- **Memory Management**: LRU eviction for memory efficiency
- **Parallel Fetching**: Concurrent data requests for multiple symbols
- **Data Validation**: Quality checks and outlier detection

#### Data Flow
```python
# 1. Check cache for existing data
cached_data = data_cache.get(symbol, timeframe)

# 2. Fetch from exchange if cache miss or expired
if not cached_data or is_expired(cached_data):
    fresh_data = await exchange.fetch_ohlcv(symbol, timeframe, limit)
    data_cache.set(symbol, timeframe, fresh_data)

# 3. Return cached data to strategy
return data_cache.get(symbol, timeframe)
```

#### Performance Optimizations
- **Batch Requests**: Group multiple symbol requests
- **Compression**: Efficient storage of historical data
- **Prefetching**: Anticipatory data loading
- **Shared Access**: Single fetch serves multiple strategies

### Portfolio Generation and Combination

The portfolio combination process intelligently merges strategy outputs:

#### Strategy Portfolio Generation
Each strategy generates a target portfolio:
```python
class StrategyResult:
    strategy_name: str
    target_positions: List[StrategyPosition]
    total_capital_allocated: float
    execution_time_seconds: float
    success: bool
```

#### Position Combination Algorithm
```python
def combine_portfolios(strategy_results, strategy_weights):
    # 1. Apply strategy weights to positions
    weighted_positions = apply_weights(strategy_results, strategy_weights)

    # 2. Group positions by symbol
    symbol_groups = group_by_symbol(weighted_positions)

    # 3. Net long/short positions for each symbol
    netted_positions = []
    for symbol, positions in symbol_groups.items():
        long_total = sum(pos.size_usd for pos in positions if pos.side == 'long')
        short_total = sum(pos.size_usd for pos in positions if pos.side == 'short')

        net_size = long_total - short_total
        if abs(net_size) >= min_position_size:
            final_side = 'long' if net_size > 0 else 'short'
            netted_positions.append(CombinedPosition(
                symbol=symbol,
                side=final_side,
                size_usd=abs(net_size),
                contributing_strategies=get_contributors(positions)
            ))

    return netted_positions
```

#### Attribution Tracking
Each combined position maintains:
- **Contributing Strategies**: Which strategies contributed
- **Strategy Contributions**: USD contribution from each strategy
- **Confidence Scores**: Weighted average confidence
- **Metadata**: Strategy-specific information

### Execution Engine

The execution engine handles trade placement with sophisticated order management:

#### Order Execution Strategy
```python
class RandomizedExecutor:
    async def execute_to_target_positions(self, target_positions):
        # 1. Calculate position deltas
        current_positions = await exchange.fetch_positions()
        deltas = calculate_deltas(current_positions, target_positions)

        # 2. Generate randomized order batches
        order_batches = create_random_batches(deltas, batch_size=(1, 5))

        # 3. Execute batches with random intervals
        for batch in order_batches:
            await execute_batch(batch)
            await asyncio.sleep(random.uniform(30, 300))  # Random delay
```

#### Order Placement Logic
- **Post-Only Preference**: Attempts post-only orders first for better fees
- **Orderbook Level Selection**: Random levels 5-10 from best bid/ask
- **Retry Logic**: Fallback to taker orders if post-only fails
- **Position Tolerance**: Buffer zones to prevent over-trading

#### Risk Controls
- **Position Limits**: Maximum position size validation
- **Spread Checks**: Reject orders with excessive spreads
- **Balance Validation**: Ensure sufficient margin before trading
- **Emergency Stops**: Immediate position closure capabilities

### Maintenance and Monitoring

The system includes comprehensive monitoring and maintenance features:

#### Operational Monitoring
```python
# Position monitoring at scheduled intervals
async def monitor_positions():
    positions = await exchange.fetch_positions()
    active_positions = [pos for pos in positions if pos['contracts'] != 0]

    # Log position status
    for pos in active_positions:
        logger.info(f"{pos['symbol']}: {pos['side']} {pos['contracts']}")

    # Update state and performance tracking
    state_manager.update_positions(active_positions)
    performance_tracker.record_monitoring_data(active_positions)
```

#### Performance Tracking
- **Real-time Metrics**: Position P&L, execution times, success rates
- **Historical Analysis**: Performance trends and attribution
- **Risk Metrics**: Volatility, drawdown, Sharpe ratio calculation
- **Export Capabilities**: Data export for external analysis tools

#### Error Handling and Recovery
```python
class ErrorRecoveryManager:
    async def handle_strategy_failure(self, strategy_name, error):
        # Log error details
        logger.error(f"Strategy {strategy_name} failed: {error}")

        # Attempt recovery based on error type
        if isinstance(error, NetworkError):
            await self.retry_with_backoff(strategy_name)
        elif isinstance(error, DataError):
            await self.refresh_data_cache()
        else:
            await self.disable_strategy(strategy_name)
```

#### Maintenance Tasks
- **Log Rotation**: Automatic log file management
- **Cache Cleanup**: Periodic removal of stale data
- **Performance Optimization**: Memory usage monitoring
- **State Persistence**: Regular state backup and recovery

## Available Strategies

### Statistical Arbitrage Carry Trade
**Location**: `src/strategies/stat_arb_carry_trade/`

A funding arbitrage strategy that exploits funding rate differentials in cryptocurrency perpetual futures markets.

**Key Features**:
- Universe: Volume and volatility filtered perpetual futures
- Signal: Annualized 3-day mean funding rates with fixed 10.95% cost adjustment
- Selection: Top 5 approach (most negative for longs, most positive for shorts)
- Sizing: Equal dollar weighting with volatility targeting and optional beta neutrality
- Risk Management: Portfolio EV thresholds and position buffer zones

### Cross-Sectional Momentum (Example)
**Location**: `src/strategies/cross_sectional_momentum/`

A momentum-based strategy using price return z-scores for position selection.

**Key Features**:
- Universe: Top 50 coins by market cap (excluding stablecoins)
- Signal: 20-day z-score of daily close price returns
- Selection: Momentum thresholds with sigmoid weighting
- Sizing: Volatility-targeted with beta neutrality
- Risk Management: Momentum decay and reversal protection

## Features

### Multi-Strategy Framework
- **Parallel Execution**: Strategies run independently and concurrently
- **Intelligent Combination**: Automatic position netting and weight application
- **Performance Attribution**: Individual strategy and portfolio tracking
- **Resource Efficiency**: Shared data fetching and caching
- **Extensible Design**: Easy addition of new strategies

### Trading Capabilities
- **Multi-Exchange Support**: Bybit, Binance, OKX, Hyperliquid
- **Advanced Order Management**: Post-only preference with fallback logic
- **Risk Management**: Position limits, spread checks, emergency controls
- **Market Neutrality**: Beta projection and volatility targeting
- **Cost Optimization**: Exchange-specific fee structures

### Operational Features
- **Robust Error Handling**: Graceful degradation and recovery
- **Comprehensive Logging**: Detailed execution and performance logs
- **State Persistence**: Automatic state backup and recovery
- **Performance Monitoring**: Real-time metrics and historical analysis
- **Configuration Management**: Hierarchical configuration system

## Requirements

### System Requirements
- Python 3.8 or higher
- 4GB RAM minimum (8GB recommended)
- Stable internet connection
- Operating System: Windows, macOS, or Linux

### Python Dependencies
See `requirements.txt` for complete list. Key dependencies:
- `ccxt`: Exchange connectivity
- `numpy`: Numerical computations
- `pandas`: Data manipulation
- `aiohttp`: Async HTTP client
- `pyyaml`: Configuration management

### Exchange Requirements
- Valid exchange account with API access
- Sufficient margin for trading
- API key with trading permissions
- Testnet access recommended for initial testing

## License

This project is licensed under the MIT License. See the LICENSE file for details.
- **Buffer zone trading**: Over-trading prevention with configurable tolerance zones

### Execution & Risk Management
- **Randomized batch execution**: Reduces market impact with fresh orderbook data
- **Post-only order optimization**: Minimizes trading costs with retry logic
- **Real-time monitoring**: Continuous position tracking and alignment checking
- **Exchange-specific optimization**: Tailored trading costs and execution parameters
- **Comprehensive state management**: Persistent strategy state across restarts

## Project Structure

```
funding_arb/
├── main.py                 # Entry point
├── config.yaml            # Configuration file
├── requirements.txt        # Dependencies
├── setup.py               # Setup script
├── logs/                  # Log files
└── src/                   # Source code
    ├── config/            # Configuration management
    ├── core/              # Strategy implementation
    ├── data/              # Data fetching and analysis
    ├── exchanges/         # Exchange interfaces
    ├── execution/         # Order execution
    ├── storage/           # State management
    └── utils/             # Utility functions
```

## Configuration

### Basic Settings
```yaml
# Exchange selection
exchange: bybit                    # bybit, binance, okx, hyperliquid
use_testnet: true                 # Use testnet environment
use_demo: false                   # Use demo account (if available)

# Capital allocation
total_capital_usd: 10000          # Total capital to deploy

# Position Selection
max_position_capital_pct: 25      # Maximum capital per position (25%)
max_positions_per_leg: 5          # Fixed 5 positions per leg

# Market filters
min_daily_volume_usd: 1000000     # Minimum 5-day mean volume filter ($1M)
max_abs_funding_rate: 0.01        # Maximum absolute funding rate (±1%)
min_volatility_threshold: 0.05    # Minimum volatility to exclude stablecoins (5%)
exclude_new_listings_days: 60     # Exclude coins listed within 60 days

# Trading costs (exchange-specific with floor protection)
trading_cost_adjustment:
  bybit: 0.0001                   # 0.01% for Bybit
  binance: 0.0001                 # 0.01% for Binance
  okx: 0.0001                     # 0.01% for OKX
  hyperliquid: 0.0000125          # 0.00125% for Hyperliquid (lower fees)

# Timing configuration
max_sleep_seconds: 3600           # Maximum sleep duration between checks (1 hour)
default_sleep_seconds: 3600       # Default sleep when no events scheduled (1 hour)
monitoring_window_seconds: 300    # Window around monitoring time (5 minutes)

# Cache Configuration (Memory Management)
cache_default_ttl: 300            # Default cache TTL in seconds (5 minutes)
cache_max_size: 1000              # Maximum cache entries
cache_gc_interval: 300            # Garbage collection interval in seconds (5 minutes)
```

### Credential Setup
```yaml
# Standard exchanges (Bybit, Binance)
api_key: "your_api_key"
api_secret: "your_api_secret"

# OKX (requires passphrase)
passphrase: "your_passphrase"

# Hyperliquid (uses wallet)
wallet_address: "your_wallet_address"
private_key: "your_private_key"
```

## Strategy Logic

The strategy operates by:

1. **Market Analysis**: Fetches funding rates and volume data from the selected exchange
2. **Filtering**: Applies 5-day mean volume and funding rate filters to identify eligible markets
3. **Position Selection**: Selects equal numbers of long and short positions based on funding rates
4. **Order Execution**: Places limit orders with randomized timing and pricing
5. **Monitoring**: Tracks positions and rebalances at scheduled intervals

### Position Selection Process
- **Funding Rate Analysis**: Calculates annualized 3-day mean funding rates for all eligible markets
- **Intelligent Cost Adjustment**:
  - New positions: Apply 10.95% annualized trading cost adjustment with floor protection
  - Held positions: Use raw annualized funding rates (no cost penalty)
  - Floor protection: Positive rates can't go negative, negative rates can't go positive
- **Top 5 Market Selection**:
  - **Fixed Position Counts**: Selects exactly 5 positions per leg
  - **Simple Ranking**: Ranks by adjusted annualized funding rate
  - **Long positions**: Top 5 most negative adjusted funding rates (we get paid to long)
  - **Short positions**: Top 5 most positive adjusted funding rates (we get paid to short)
- **Portfolio Construction**:
  - **Equal Dollar Weighting**: Starts with equal dollar weights (1/N) for each position
  - **Equal Dollar Volatility**: Maintains equal dollar volatility between long and short legs
  - **Maximum Capital Constraint**: Limits individual positions to 25% of total capital
  - **Minimum Leg Size**: Ensures at least 2 positions per leg for diversification
- **Volume Filtering**: Uses 5-day mean volume above minimum threshold
- **Volatility Screening**: Excludes low-volatility assets (likely stablecoins)

### Order Execution
- **Buffer Zone Trading** (Prevents Over-Trading):
  - Creates buffer zones around target positions (configurable % of position size)
  - **No-Trade Zone**: If current position is within buffer, no trades executed
  - **Smart Rebalancing**: Only trades to buffer edges, not exact targets
  - **Example**: 5% buffer on $1000 target = trade only if position outside $950-$1050 range
- **Randomized Batch Execution**:
  - Divides orders into 3-5 random batches per coin
  - Random timing intervals (60-300 seconds between batches)
  - Random delta allocation (15-35% per batch)
- **Smart Order Placement**:
  - Uses limit orders with post-only flag and fresh orderbook data
  - Places orders at random orderbook levels (3-7 levels deep)
  - Fetches fresh orderbook immediately before each order
- **Retry Logic & Reconciliation**:
  - Implements retry logic with fresh orderbook fetching for post-only rejections
  - Cancels all open orders between execution iterations for clean reconciliation
  - Continues until position delta is within 0.25% of target
- **Concurrent Execution**: Processes multiple coins simultaneously with rate limiting

## Environment Variables

Alternative to configuration file credentials:

```bash
# Bybit
export BYBIT_API_KEY="your_key"
export BYBIT_API_SECRET="your_secret"

# Binance
export BINANCE_API_KEY="your_key"
export BINANCE_API_SECRET="your_secret"

# OKX
export OKX_API_KEY="your_key"
export OKX_API_SECRET="your_secret"
export OKX_PASSPHRASE="your_passphrase"

# Hyperliquid
export HYPERLIQUID_WALLET_ADDRESS="your_address"
export HYPERLIQUID_PRIVATE_KEY="your_key"
```

## Usage

### Initial Setup
1. Configure `config.yaml` with your preferred settings
2. Set up API credentials (file or environment variables)
3. Start with testnet mode for testing

### Interactive Setup
If no credentials are found, the strategy will prompt for interactive setup:

```
🔧 Credential Setup
========================================
Exchange (bybit/binance/okx/hyperliquid) [bybit]: bybit
Bybit API Key: your_api_key
Bybit API Secret: your_api_secret
Use testnet? (y/n) [y]: y
Use demo trading? (y/n) [n]: n
Total capital USD [10000]: 5000
Enable simulation mode (no real trades)? (y/n) [y]: y
```

**Simulation Mode Options:**
- `y` (Yes): Paper trading only - no real orders placed
- `n` (No): Live trading - real orders will be executed

### Running the Strategy
```bash
python main.py
```

The system will:
- Load configuration and validate settings
- Connect to the specified exchange
- Analyze markets and select positions
- Execute orders and monitor performance (unless in simulation mode)
- Rebalance daily at 23:00 UTC and monitor at 23:00, 07:00, 15:00 UTC

### Checking Position Alignment
```bash
# Check if current positions match targets
python check_positions.py

# Check with custom tolerance
python check_positions.py --tolerance 0.01

# Verbose output
python check_positions.py --verbose
```

### Monitoring
- Check console output for real-time status
- Review logs in `logs/statarb_carry_trade.log`
- Monitor strategy state in `strategy_state.json`

## Key Components

### Data Fetcher (`src/data/fetcher.py`)
- Fetches market data and funding rates from exchanges
- Implements caching to reduce API calls
- Handles rate limiting and error recovery

### Data Analyzer (`src/data/analyzer.py`)
- Analyzes funding rates and volatility
- Selects optimal position pairs
- Calculates position sizes based on volatility

### Randomized Executor (`src/execution/randomized_executor.py`)
- Executes positions using randomized batch execution with fresh orderbook data
- Implements delta-based trading with post-only order optimization
- Provides concurrent execution across multiple coins with rate limiting
- Includes automatic position reconciliation and retry logic for post-only rejections

### Position Manager (`src/execution/position_manager.py`)
- Manages portfolio positions and calculates volatility-targeted sizing
- Calculates required trades for rebalancing
- Monitors position performance and risk metrics

### State Manager (`src/storage/state_manager.py`)
- Persists strategy timing state across restarts
- Tracks last rebalance and monitoring times
- Provides timing continuity for strategy scheduling

## Risk Management

The strategy implements comprehensive risk management features:

### Position Risk Controls
- **Market Neutrality**: Equal dollar volatility between long and short legs (not necessarily equal position counts)
- **Position Limits**: Maximum 25% of capital per individual position
- **EV-Based Selection**: Only trades coins with positive Expected Value (non-zero adjusted funding)
- **Sigmoid Risk Control**: Automatically reduces weights for extreme funding rates
- **Volatility-Targeted Sizing**: Dynamic sizing based on weighted volatility (30% × 60d + 50% × 30d + 20% × 10d)
- **Minimum Diversification**: Ensures at least 2 positions per leg
- **New Listing Protection**: Excludes coins listed within configurable days (default: 60 days)
- **Volume Filtering**: Requires minimum 5-day mean volume to ensure liquidity

### Cost Management
- **Exchange-Specific Costs**: Tailored trading cost adjustments per exchange
- **Floor Protection**: Cost adjustments cannot flip funding rate signs
- **Selective Adjustment**: Only new positions incur cost adjustment, held positions use raw rates

### Operational Risk Controls
- **State Persistence**: Strategy state saved across restarts
- **Error Handling**: Comprehensive exception handling with graceful degradation
- **Rate Limiting**: Adaptive API rate limiting to prevent exchange restrictions
- **Monitoring**: Continuous position tracking with alignment verification

### Recommended Practices
- **Testing**: Start with testnet environment for thorough testing
- **Capital Management**: Use conservative EV thresholds initially (lower peak_abs_funding_rate)
- **Performance Monitoring**: Track funding collection vs trading costs regularly
- **Log Review**: Monitor logs for system health and performance metrics
- **Gradual Scaling**: Increase position sizes gradually as confidence builds

## Troubleshooting

### Common Issues

**Connection Errors**
- Verify API credentials are correct
- Check network connectivity
- Ensure sufficient account balance

**No Eligible Markets**
- Adjust 5-day mean volume and funding rate filters
- Check current market conditions
- Verify exchange market availability

**Order Execution Issues**
- Check position limits on exchange
- Verify margin requirements
- Review orderbook liquidity

### Debug Information
- Enable debug mode in configuration
- Check logs in `logs/statarb_carry_trade.log`
- Monitor strategy state in `strategy_state.json`

## Technical Details

### Data Processing
- Fetches market data and funding rates from exchange APIs
- Implements caching to reduce redundant API calls
- Uses concurrent processing for improved performance
- Handles rate limiting and connection errors

### EV-Based Position Sizing
- **Sigmoid Weighting**: Uses `x * exp(-(x/peak)²)` function to allocate capital based on Expected Value
- **Volatility-Targeted Sizing**: Uses weighted volatility calculation (30% × 60d + 50% × 30d + 20% × 10d)
- **Dynamic Leverage**: Adjusts leverage based on target volatility vs. weighted volatility
- **Equal Dollar Volatility**: Balances long and short legs to have equal dollar volatility for market neutrality
- **Maximum Position Limits**: Caps individual positions at 25% of total capital
- **Cost-Aware Selection**: Applies intelligent cost adjustment with floor protection
- **Volume-Weighted Ranking**: Uses volume as tie-breaker for equal EV weights

### Order Management
- Places limit orders with post-only flag when supported
- Implements randomized timing to reduce market impact
- Uses retry logic for failed or partially filled orders
- Tracks order status and maintains persistent state

### Timing Schedule
- **Rebalancing**: Daily at 23:00 UTC (1 hour before 00:00 funding)
- **Monitoring**: At 23:00, 07:00, 15:00 UTC (1 hour before each funding time)
- **Funding Times**: 00:00, 08:00, 16:00 UTC (standard for most exchanges)
- **Sleep Duration**: 1 hour between checks (configurable)

### Timing Configuration
```yaml
# Timing settings
max_sleep_seconds: 3600           # Maximum sleep duration between checks (1 hour)
default_sleep_seconds: 3600       # Default sleep when no events scheduled (1 hour)
monitoring_window_seconds: 300    # Window around monitoring time to trigger check (5 minutes)
```

### State Persistence
- Saves strategy timing state to JSON files
- Tracks rebalance and monitoring schedules across restarts
- Maintains execution timing continuity
- Provides scheduling recovery capabilities

### Performance Optimizations
- **Concurrent Data Fetching**: Parallel processing for market analysis and position calculations
- **Intelligent Caching**: Smart caching system with TTL and automatic garbage collection
- **Memory Management**: Configurable garbage collection to prevent memory leaks
- **Thread-Safe Operations**: Thread-safe cache operations for concurrent access
- **Batch Processing**: Processes symbols in configurable batches for memory efficiency
- **Rate Limiting**: Adaptive rate limiting to prevent exchange restrictions

### Data Quality & Validation
- **NaN Data Cleaning**: Automatic detection and removal of NaN/invalid data rows
- **Unit Consistency**: Comprehensive data unit validation across all exchanges
- **Contract Specifications**: Proper handling of minimum tradeable quantities
- **Volume Conversion**: Accurate base→quote volume conversion with validation
- **Funding Rate Validation**: Cleaning of extreme or invalid funding rate entries
- **Error Recovery**: Robust error handling with graceful degradation

### Exchange Integration
- **Unified Interface**: Consistent API across Bybit, Binance, OKX, and Hyperliquid
- **Exchange-Specific Optimization**: Tailored parameters for each exchange's characteristics
- **Demo/Testnet Support**: Comprehensive testing environment support
- **Credential Management**: Flexible credential handling (config, environment, interactive)

## Configuration Examples

### Conservative Setup (Recommended for Beginners)
```yaml
total_capital_usd: 5000           # Start with smaller capital
peak_abs_funding_rate: 0.0005     # Conservative peak for sigmoid (0.05%)
max_abs_funding_rate: 0.005       # Conservative funding rate limit (0.5%)
max_position_capital_pct: 15      # Conservative max per position (15%)
min_positions_per_leg: 3          # Minimum diversification
min_daily_volume_usd: 2000000     # Higher volume requirement ($2M)
simulation_mode: true             # Paper trading first
use_testnet: true                 # Use testnet environment
```

### Aggressive Setup (For Experienced Users)
```yaml
total_capital_usd: 50000          # Larger capital deployment
peak_abs_funding_rate: 0.001      # Standard peak for sigmoid (0.1%)
max_abs_funding_rate: 0.01        # Higher funding rate tolerance (1%)
max_position_capital_pct: 25      # Standard max per position (25%)
min_positions_per_leg: 2          # Minimum diversification
min_daily_volume_usd: 1000000     # Standard volume requirement ($1M)
simulation_mode: false            # Live trading
use_testnet: false                # Live environment
```

### High-Frequency Setup (Maximum Opportunities)
```yaml
total_capital_usd: 100000         # Large capital for diversification
peak_abs_funding_rate: 0.0015     # Higher peak for more opportunities (0.15%)
max_abs_funding_rate: 0.015       # High tolerance for opportunities (1.5%)
max_position_capital_pct: 30      # Higher max per position (30%)
min_positions_per_leg: 2          # Minimum diversification
min_daily_volume_usd: 500000      # Lower volume requirement ($500K)
max_sleep_seconds: 1800           # More frequent checks (30 minutes)
```

## Best Practices & Tips

### Getting Started
1. **Start Small**: Begin with testnet and small capital amounts
2. **Monitor Closely**: Watch the first few rebalancing cycles carefully
3. **Understand Costs**: Ensure funding collection exceeds trading costs
4. **Test Thoroughly**: Run in simulation mode before live trading

### Optimization Tips
- **EV Thresholds**: Start with conservative peak_abs_funding_rate (0.0005), increase gradually as you gain confidence
- **Position Limits**: Begin with lower max_position_capital_pct (15-20%), increase as strategy proves profitable
- **Volume Filtering**: Higher volume requirements reduce slippage but limit opportunities
- **Funding Rate Limits**: Conservative limits reduce risk but may miss profitable opportunities
- **Sleep Intervals**: Longer intervals save resources, shorter intervals catch more opportunities

### Monitoring & Maintenance
- **Daily Review**: Check logs and performance metrics daily
- **Weekly Analysis**: Analyze funding collection vs. trading costs weekly
- **Monthly Optimization**: Adjust parameters based on market conditions monthly
- **Backup Strategy**: Always have a plan for manual intervention if needed

## Frequently Asked Questions

### Strategy Questions

**Q: Why use mean instead of median funding rates?**
A: Mean funding rates are more sensitive to recent changes and provide better signal quality for trend detection, while median can mask important shifts in funding dynamics.

**Q: How does the cost adjustment floor protection work?**
A: Cost adjustments cannot flip the sign of funding rates. If a positive funding rate minus cost would go negative, it's floored at zero. Similarly, negative rates plus cost are ceilinged at zero.

**Q: How does the EV-based approach differ from percentile-based selection?**
A: EV-based selection uses all coins with positive Expected Value (non-zero adjusted funding) rather than fixed percentiles. It applies sigmoid weighting to allocate capital based on funding rate strength, allowing for more dynamic position counts and better capital utilization.

**Q: What is the sigmoid weighting function?**
A: The function `x * exp(-(x/peak)²)` maps funding rates to position weights. It peaks at the configured `peak_abs_funding_rate` and approaches zero for extreme values, automatically controlling risk while maximizing opportunities.

**Q: Why equal dollar volatility instead of equal position counts?**
A: Equal dollar volatility ensures true market neutrality by balancing risk exposure between legs. This allows for different numbers of longs vs shorts based on market opportunities while maintaining overall portfolio balance.

### Technical Questions

**Q: How often does the strategy rebalance?**
A: Daily at 23:00 UTC (1 hour before funding), with monitoring checks at 23:00, 07:00, and 15:00 UTC.

**Q: What happens if the strategy crashes?**
A: The strategy saves state persistently and can resume from the last known state. Timing continuity is maintained across restarts.

**Q: How does the randomized execution work?**
A: Orders are split into 3-5 random batches with random timing (60-300s intervals) and random orderbook placement to reduce market impact.

**Q: "No eligible coins found" - what should I do?**
A: Check your filters - reduce `min_daily_volume_usd`, increase `max_abs_funding_rate`, or decrease `min_volatility_threshold`.

**Q: Orders keep getting rejected - why?**
A: Likely post-only rejections due to stale orderbook data. The strategy automatically retries with fresh orderbook data.

## Testing

The strategy includes comprehensive unit tests covering all major components with **54 total tests** across **6 specialized test modules**:

### Test Coverage
- **Sigmoid Function Tests**: Mathematical properties, edge cases, monotonicity
- **Position Selection Tests**: EV-based selection logic, filtering, ranking
- **Position Sizing Tests**: Capital allocation, volatility balancing, constraints
- **Volatility Calculation Tests**: Weighted volatility, dollar volatility balancing
- **Configuration Validation Tests**: Parameter validation, bounds checking
- **End-to-End Integration Tests**: Complete strategy pipeline testing

### Running Tests

```bash
# Run comprehensive test suite with detailed reporting
cd tests
python run_all_ev_tests.py

# Run individual test modules
python -m pytest test_ev_sigmoid_function.py -v
python -m pytest test_ev_position_selection.py -v
python -m pytest test_ev_position_sizing.py -v
python -m pytest test_ev_volatility_calculations.py -v
python -m pytest test_ev_config_validation.py -v
python -m pytest test_ev_end_to_end.py -v

# Generate test report
# Report will be saved as tests/EV_STRATEGY_TEST_REPORT.md
```

### Test Results Summary
- **Total Tests**: 54
- **Core Mathematical Functions**: ✅ Verified
- **Business Logic**: ✅ Thoroughly tested
- **Edge Cases**: ✅ Comprehensively covered
- **Integration**: ✅ End-to-end pipeline tested

### Mathematical Formula Verification
- **Sigmoid Weighting**: `x * exp(-(x/peak)²)` ✅ Verified
- **Weighted Volatility**: `0.3×vol60 + 0.5×vol30 + 0.2×vol10` ✅ Verified
- **Dollar Volatility Balancing**: Equal dollar vol between legs ✅ Verified
- **Volatility Targeting**: `target_vol/coin_vol` with leverage bounds ✅ Verified
