"""
Data analysis for volatility calculations and position selection
"""

import asyncio
import logging
import statistics
import numpy as np
from typing import Dict, List, Optional, Any, Tuple

from data.fetcher import DataFetcher
from data.cache import CacheKey
from data.beta_calculator import BetaCalculator

logger = logging.getLogger(__name__)


class DataAnalyzer:
    """Data analysis for volatility calculations and position selection"""
    
    def __init__(self, data_fetcher: DataFetcher, config: Dict[str, Any]):
        self.data_fetcher = data_fetcher
        self.config = config

        # Initialize beta calculator if beta projection is enabled
        if config.get('enable_beta_projection', False):
            self.beta_calculator = BetaCalculator(data_fetcher, config)
            logger.info("🔧 Beta projection enabled - initialized BetaCalculator")
        else:
            self.beta_calculator = None
            logger.info("🔧 Beta projection disabled")
    
    async def calculate_volatility_from_data(self, ohlcv_data: List[List], days: int) -> Optional[float]:
        """Calculate volatility from existing OHLCV data"""
        try:
            if len(ohlcv_data) < days + 1:
                return None
            
            # Extract closing prices for the specified period
            closes = [candle[4] for candle in ohlcv_data[-days-1:]]  # Take last N+1 days
            
            # Calculate daily returns with division by zero protection
            returns = []
            for i in range(1, len(closes)):
                if closes[i-1] == 0:
                    logger.warning(f"⚠️ Zero price detected in volatility calculation, skipping")
                    continue
                daily_return = (closes[i] - closes[i-1]) / closes[i-1]
                returns.append(daily_return)
            
            if len(returns) < days:
                return None
            
            # Calculate standard deviation of returns
            std_dev = statistics.stdev(returns)
            
            # Annualize volatility (365 days for crypto 24/7 trading)
            annualized_vol = std_dev * (365 ** 0.5)
            return annualized_vol
            
        except Exception as e:
            logger.debug(f"❌ Error calculating volatility from data: {e}")
            return None
    
    async def calculate_volatility(self, symbol: str, days: int) -> Optional[float]:
        """Calculate annualized volatility for a given number of days"""
        try:
            # Use cached OHLCV data
            ohlcv_data = await self.data_fetcher.get_cached_ohlcv(symbol, '1d', days + 1)
            if not ohlcv_data:
                return None
            
            return await self.calculate_volatility_from_data(ohlcv_data, days)
            
        except Exception as e:
            logger.error(f"❌ Error calculating volatility for {symbol} ({days}d): {e}")
            return None
    
    async def calculate_weighted_volatility(self, symbol: str) -> float:
        """Calculate weighted volatility efficiently by fetching data once.
        
        Fetches 60-day OHLCV data once and derives 30-day and 10-day periods
        from the same dataset.
        
        Formula: 30% × 60d + 50% × 30d + 20% × 10d annualized volatility
        """
        try:
            # Check cache first
            cache_key = CacheKey.volatility(symbol, 'weighted')
            cached_vol = self.data_fetcher.cache.get(cache_key)
            if cached_vol is not None:
                return cached_vol
            
            # Fetch 60-day data once and derive shorter periods from it
            ohlcv_data = await self.data_fetcher.get_cached_ohlcv(symbol, '1d', 61)  # 61 days to calculate 60-day volatility
            
            if not ohlcv_data or len(ohlcv_data) < 61:
                logger.debug(f"⚠️ Insufficient OHLCV data for {symbol} volatility calculation")
                default_vol = self.config.get('default_volatility', 0.5)
                self.data_fetcher.cache.set(cache_key, default_vol, ttl=3600)  # Cache for 1 hour
                return default_vol
            
            # Calculate all volatilities from the same dataset
            vol_60d = await self.calculate_volatility_from_data(ohlcv_data, 60)
            vol_30d = await self.calculate_volatility_from_data(ohlcv_data, 30)
            vol_10d = await self.calculate_volatility_from_data(ohlcv_data, 10)
            
            # Check if we have all required volatilities
            if vol_60d is None or vol_30d is None or vol_10d is None:
                logger.debug(f"⚠️ Missing volatility data for {symbol}, using fallback")
                # Use available data or fallback to a reasonable default
                available_vols = [v for v in [vol_60d, vol_30d, vol_10d] if v is not None]
                if available_vols:
                    weighted_vol = sum(available_vols) / len(available_vols)  # Simple average as fallback
                else:
                    weighted_vol = self.config.get('default_volatility', 0.5)  # 50% default volatility
            else:
                # Calculate weighted volatility: 30% * 60d + 50% * 30d + 20% * 10d
                weighted_vol = (0.3 * vol_60d) + (0.5 * vol_30d) + (0.2 * vol_10d)
            
            logger.debug(f"📊 {symbol} weighted volatility: {weighted_vol:.4f} ({weighted_vol*100:.2f}%) "
                        f"[60d: {vol_60d}, 30d: {vol_30d}, 10d: {vol_10d}]")
            
            # Cache the result
            self.data_fetcher.cache.set(cache_key, weighted_vol)
            return weighted_vol
            
        except Exception as e:
            logger.error(f"❌ Error calculating weighted volatility for {symbol}: {e}")
            return self.config.get('default_volatility', 0.5)  # Fallback to default

    def _sigmoid_mapping_function(self, x: float, peak_abs_funding_rate: float) -> float:
        """
        Sigmoid mapping function for EV-based position weighting.

        Formula: y = x * exp(-(x / peak_abs_funding_rate)^2)
        Normalized by y_max = peak_abs_funding_rate * exp(-1)

        Args:
            x: Adjusted funding rate (feature value)
            peak_abs_funding_rate: Peak funding rate from config

        Returns:
            Normalized weight between 0 and 1
        """
        # Validate peak_abs_funding_rate
        if peak_abs_funding_rate <= 0:
            logger.error(f"❌ Invalid peak_abs_funding_rate: {peak_abs_funding_rate}. Must be positive.")
            logger.error("❌ This indicates a configuration error that should be fixed.")
            logger.error("❌ Check your config.yaml file and ensure peak_abs_funding_rate > 0")
            raise ValueError(f"peak_abs_funding_rate must be positive, got {peak_abs_funding_rate}")

        # Validate input x
        if not isinstance(x, (int, float)) or np.isnan(x) or np.isinf(x):
            logger.warning(f"⚠️ Invalid input to sigmoid function: {x}, returning 0")
            return 0.0

        try:
            # Calculate sigmoid value with overflow protection
            ratio = x / peak_abs_funding_rate
            if abs(ratio) > 10:  # Prevent overflow in exp calculation
                logger.debug(f"⚠️ Large ratio in sigmoid: {ratio}, clamping to prevent overflow")
                ratio = 10 if ratio > 0 else -10

            y = x * np.exp(-(ratio ** 2))

            # Normalize by maximum value (occurs at x = peak_abs_funding_rate)
            y_max = peak_abs_funding_rate * np.exp(-1)

            # Return normalized weight with bounds checking
            normalized_weight = y / y_max if y_max != 0 else 0.0

            # Ensure result is within valid bounds
            return max(0.0, min(1.0, normalized_weight))

        except (OverflowError, ZeroDivisionError) as e:
            logger.warning(f"⚠️ Numerical error in sigmoid calculation: {e}, returning 0")
            return 0.0

    def select_positions_ev_based(self, eligible_coins: List[Dict[str, Any]], current_positions: Dict[str, Dict] = None) -> Tuple[List[Dict], List[Dict]]:
        """
        Select long and short positions using top 5 approach.

        Key features:
        1. Fixed 5 positions per leg
        2. Simple ranking by adjusted annualized funding rate
        3. Top 5 most negative for longs, top 5 most positive for shorts
        4. Intelligent cost adjustment for new vs held positions

        Args:
            eligible_coins: List of eligible coins with funding rates and volume data
            current_positions: Dict of currently held positions {symbol: position_data}

        Returns:
            Tuple[List[Dict], List[Dict]]: (long_candidates, short_candidates)
        """
        if not eligible_coins:
            logger.warning("⚠️ No eligible coins provided for position selection")
            return [], []

        if current_positions is None:
            current_positions = {}

        # Apply cost adjustment only to NEW coins (not currently held)
        adjusted_coins = []
        for coin in eligible_coins:
            symbol = coin['symbol']
            coin_copy = coin.copy()

            if symbol in current_positions:
                # Already held - use raw funding rate (no cost adjustment)
                coin_copy['adjusted_funding'] = coin_copy['mean_funding_3d']
                coin_copy['cost_adjusted'] = False
                logger.debug(f"📊 {symbol}: Already held, using raw funding {coin_copy['mean_funding_3d']:.6f}")
            else:
                # New coin - keep existing cost adjustment
                coin_copy['cost_adjusted'] = True
                logger.debug(f"📊 {symbol}: New coin, using adjusted funding {coin_copy['adjusted_funding']:.6f}")

            adjusted_coins.append(coin_copy)

        # Simplified position selection: Top 5 most negative for longs, Top 5 most positive for shorts
        max_positions_per_leg = 5  # Fixed to 5 positions per leg

        # Separate coins into potential longs and shorts based on adjusted funding rates
        potential_longs = []
        potential_shorts = []

        for coin in adjusted_coins:
            adjusted_funding = float(coin['adjusted_funding'])

            if adjusted_funding < 0:
                # Negative funding rates = good for longs (we get paid to long)
                coin_copy = coin.copy()
                potential_longs.append(coin_copy)
                logger.debug(f"📊 {coin['symbol']}: Long candidate, adjusted funding={adjusted_funding:.4f}")

            elif adjusted_funding > 0:
                # Positive funding rates = good for shorts (we get paid to short)
                coin_copy = coin.copy()
                potential_shorts.append(coin_copy)
                logger.debug(f"📊 {coin['symbol']}: Short candidate, adjusted funding={adjusted_funding:.4f}")

            # Skip coins with zero adjusted funding

        # Sort by adjusted annualized funding rate (primary ranking)
        # For longs: most negative first (best funding rates for longs)
        potential_longs.sort(key=lambda x: float(x['adjusted_funding']))
        # For shorts: most positive first (best funding rates for shorts)
        potential_shorts.sort(key=lambda x: float(x['adjusted_funding']), reverse=True)

        # Select top 5 from each side
        long_candidates = potential_longs[:max_positions_per_leg]
        short_candidates = potential_shorts[:max_positions_per_leg]

        # Log selection details
        logger.info(f"📊 Simplified Position Selection:")
        logger.info(f"   Strategy: Top {max_positions_per_leg} most negative adjusted funding for longs, Top {max_positions_per_leg} most positive for shorts")
        logger.info(f"   Ranking: Adjusted annualized funding rate")
        logger.info(f"   Selected {len(long_candidates)} long candidates, {len(short_candidates)} short candidates")

        if long_candidates:
            long_funding_range = f"{long_candidates[-1]['adjusted_funding']:.4f} to {long_candidates[0]['adjusted_funding']:.4f}"
            logger.info(f"   Long candidates funding range: {long_funding_range}")

        if short_candidates:
            short_funding_range = f"{short_candidates[0]['adjusted_funding']:.4f} to {short_candidates[-1]['adjusted_funding']:.4f}"
            logger.info(f"   Short candidates funding range: {short_funding_range}")

        return long_candidates, short_candidates




    async def enrich_coins_with_volatility(self, coins: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Enrich coin data with volatility and optionally beta calculations"""
        if self.beta_calculator:
            logger.info(f"📊 Calculating volatility and beta for {len(coins)} coins...")
        else:
            logger.info(f"📊 Calculating volatility for {len(coins)} coins...")

        # Calculate volatility and beta for all coins concurrently in batches to avoid overwhelming the system
        batch_size = self.config.get('concurrent_calculation_batch_size', 10)  # Process 10 coins at a time
        all_volatilities = []
        all_betas = []

        for i in range(0, len(coins), batch_size):
            batch = coins[i:i + batch_size]

            # Create tasks for this batch
            volatility_tasks = [self.calculate_weighted_volatility(coin['symbol']) for coin in batch]

            if self.beta_calculator:
                beta_tasks = [self.beta_calculator.calculate_beta(coin['symbol']) for coin in batch]
                # Execute both volatility and beta calculations concurrently for this batch
                batch_volatilities, batch_betas = await asyncio.gather(
                    asyncio.gather(*volatility_tasks, return_exceptions=True),
                    asyncio.gather(*beta_tasks, return_exceptions=True)
                )
                all_betas.extend(batch_betas)
            else:
                # Only calculate volatilities for this batch
                batch_volatilities = await asyncio.gather(*volatility_tasks, return_exceptions=True)

            all_volatilities.extend(batch_volatilities)
            if self.config.get('debug_mode', False):
                logger.debug(f"📊 Processed batch {i//batch_size + 1}/{(len(coins) + batch_size - 1)//batch_size}")

        volatilities = all_volatilities
        betas = all_betas if self.beta_calculator else None

        # Add volatility and beta data to coins
        enriched_coins = []
        for i, coin in enumerate(coins):
            volatility = volatilities[i]
            if isinstance(volatility, Exception):
                logger.warning(f"⚠️ Failed to calculate volatility for {coin['symbol']}: {volatility}")
                volatility = self.config.get('default_volatility', 0.5)

            # Filter out coins with too low volatility (likely stablecoins)
            min_volatility = self.config.get('min_volatility_threshold', 0.05)
            if volatility < min_volatility:
                if self.config.get('debug_mode', False):
                    logger.debug(f"⏭️ Skipping {coin['symbol']} - too low volatility ({volatility*100:.2f}%)")
                continue

            coin_copy = coin.copy()
            coin_copy['weighted_volatility'] = volatility

            # Add beta data only if beta projection is enabled
            if self.beta_calculator and betas:
                beta = betas[i]
                if isinstance(beta, Exception):
                    logger.warning(f"⚠️ Failed to calculate beta for {coin['symbol']}: {beta}")
                    beta = self.config.get('default_beta', 1.0)
                coin_copy['beta'] = beta

            enriched_coins.append(coin_copy)

        if self.beta_calculator:
            logger.info(f"✅ Enriched {len(enriched_coins)} coins with volatility and beta data")
        else:
            logger.info(f"✅ Enriched {len(enriched_coins)} coins with volatility data")
        return enriched_coins
